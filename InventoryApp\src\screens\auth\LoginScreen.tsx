import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Snackbar,
  Switch,
  Divider,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootState, AppDispatch } from '../../store';
import { loginUser, clearError } from '../../store/slices/authSlice';
import { setLanguage } from '../../store/slices/appSlice';
import { AuthStackParamList } from '../../navigation/AuthNavigator';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

type LoginScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'Login'>;

interface Props {
  navigation: LoginScreenNavigationProp;
}

const LoginScreen: React.FC<Props> = ({ navigation }) => {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const { language, isRTL } = useSelector((state: RootState) => state.app);

  const [formData, setFormData] = useState({
    username: 'admin',
    password: 'admin123',
  });
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLogin = async () => {
    if (!formData.username.trim() || !formData.password.trim()) {
      return;
    }

    try {
      await dispatch(loginUser({
        username: formData.username.trim(),
        password: formData.password,
      })).unwrap();
    } catch (error) {
      // Error is handled by the slice
    }
  };

  const toggleLanguage = () => {
    const newLanguage = language === 'en' ? 'ar' : 'en';
    dispatch(setLanguage(newLanguage));
    i18n.changeLanguage(newLanguage);
  };

  const navigateToRegister = () => {
    navigation.navigate('Register');
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        {/* Language Toggle */}
        <View style={[styles.languageToggle, isRTL && styles.languageToggleRTL]}>
          <Text style={styles.languageText}>
            {language === 'en' ? 'العربية' : 'English'}
          </Text>
          <Switch
            value={language === 'ar'}
            onValueChange={toggleLanguage}
            color={colors.primary}
          />
        </View>

        {/* Logo and Title */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <Icon name="inventory" size={80} color={colors.primary} />
          </View>
          <Text style={[styles.title, isRTL && styles.titleRTL]}>
            {t('navigation.inventory')}
          </Text>
          <Text style={[styles.subtitle, isRTL && styles.subtitleRTL]}>
            {t('auth.welcomeBack')}
          </Text>
        </View>

        {/* Login Form */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.cardTitle, isRTL && styles.cardTitleRTL]}>
              {t('auth.login')}
            </Text>

            <TextInput
              label={t('auth.username')}
              value={formData.username}
              onChangeText={(value) => handleInputChange('username', value)}
              mode="outlined"
              style={[styles.input, isRTL && styles.inputRTL]}
              left={<TextInput.Icon icon="account" />}
              autoCapitalize="none"
              autoCorrect={false}
              textAlign={isRTL ? 'right' : 'left'}
            />

            <TextInput
              label={t('auth.password')}
              value={formData.password}
              onChangeText={(value) => handleInputChange('password', value)}
              mode="outlined"
              style={[styles.input, isRTL && styles.inputRTL]}
              left={<TextInput.Icon icon="lock" />}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              secureTextEntry={!showPassword}
              autoCapitalize="none"
              autoCorrect={false}
              textAlign={isRTL ? 'right' : 'left'}
            />

            <View style={[styles.rememberMeContainer, isRTL && styles.rememberMeContainerRTL]}>
              <Switch
                value={rememberMe}
                onValueChange={setRememberMe}
                color={colors.primary}
              />
              <Text style={[styles.rememberMeText, isRTL && styles.rememberMeTextRTL]}>
                {t('auth.rememberMe')}
              </Text>
            </View>

            <Button
              mode="contained"
              onPress={handleLogin}
              loading={isLoading}
              disabled={isLoading || !formData.username.trim() || !formData.password.trim()}
              style={styles.loginButton}
              contentStyle={styles.loginButtonContent}
            >
              {t('auth.login')}
            </Button>

            <Divider style={styles.divider} />

            <View style={styles.registerContainer}>
              <Text style={[styles.registerText, isRTL && styles.registerTextRTL]}>
                {t('auth.dontHaveAccount')}
              </Text>
              <Button
                mode="text"
                onPress={navigateToRegister}
                style={styles.registerButton}
              >
                {t('auth.register')}
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Demo Credentials */}
        <Card style={styles.demoCard}>
          <Card.Content>
            <Text style={[styles.demoTitle, isRTL && styles.demoTitleRTL]}>
              Demo Credentials
            </Text>
            <Text style={[styles.demoText, isRTL && styles.demoTextRTL]}>
              Username: admin
            </Text>
            <Text style={[styles.demoText, isRTL && styles.demoTextRTL]}>
              Password: admin123
            </Text>
          </Card.Content>
        </Card>
      </ScrollView>

      <Snackbar
        visible={!!error}
        onDismiss={() => dispatch(clearError())}
        duration={4000}
        action={{
          label: t('common.ok'),
          onPress: () => dispatch(clearError()),
        }}
      >
        {error}
      </Snackbar>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    padding: spacing.md,
    justifyContent: 'center',
  },
  languageToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
    marginBottom: spacing.lg,
  },
  languageToggleRTL: {
    alignSelf: 'flex-start',
    flexDirection: 'row-reverse',
  },
  languageText: {
    marginHorizontal: spacing.sm,
    fontSize: 16,
    color: colors.onSurface,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.primaryContainer,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  titleRTL: {
    textAlign: 'right',
  },
  subtitle: {
    fontSize: 16,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
  subtitleRTL: {
    textAlign: 'right',
  },
  card: {
    marginBottom: spacing.lg,
    borderRadius: 16,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  cardTitleRTL: {
    textAlign: 'right',
  },
  input: {
    marginBottom: spacing.md,
  },
  inputRTL: {
    textAlign: 'right',
  },
  rememberMeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  rememberMeContainerRTL: {
    flexDirection: 'row-reverse',
  },
  rememberMeText: {
    marginLeft: spacing.sm,
    color: colors.onSurface,
  },
  rememberMeTextRTL: {
    marginLeft: 0,
    marginRight: spacing.sm,
    textAlign: 'right',
  },
  loginButton: {
    marginBottom: spacing.md,
    borderRadius: 8,
  },
  loginButtonContent: {
    paddingVertical: spacing.xs,
  },
  divider: {
    marginVertical: spacing.md,
  },
  registerContainer: {
    alignItems: 'center',
  },
  registerText: {
    color: colors.onSurfaceVariant,
    marginBottom: spacing.xs,
  },
  registerTextRTL: {
    textAlign: 'right',
  },
  registerButton: {
    marginTop: spacing.xs,
  },
  demoCard: {
    backgroundColor: colors.tertiaryContainer,
    borderRadius: 12,
  },
  demoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onTertiaryContainer,
    marginBottom: spacing.sm,
  },
  demoTitleRTL: {
    textAlign: 'right',
  },
  demoText: {
    fontSize: 14,
    color: colors.onTertiaryContainer,
    marginBottom: spacing.xs,
  },
  demoTextRTL: {
    textAlign: 'right',
  },
});

export default LoginScreen;
