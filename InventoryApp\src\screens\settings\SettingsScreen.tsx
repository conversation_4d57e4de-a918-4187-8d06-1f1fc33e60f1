import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  List,
  Switch,
  Divider,
  Button,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { setLanguage, setTheme, setCurrency } from '../../store/slices/appSlice';
import { logoutUser } from '../../store/slices/authSlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const SettingsScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { language, theme, currency, isRTL } = useSelector((state: RootState) => state.app);
  const { user } = useSelector((state: RootState) => state.auth);

  const handleLanguageToggle = () => {
    const newLanguage = language === 'en' ? 'ar' : 'en';
    dispatch(setLanguage(newLanguage));
    i18n.changeLanguage(newLanguage);
  };

  const handleThemeToggle = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    dispatch(setTheme(newTheme));
  };

  const handleLogout = () => {
    dispatch(logoutUser());
  };

  const settingSections = [
    {
      title: t('settings.general'),
      items: [
        {
          title: t('settings.language'),
          description: language === 'en' ? 'English' : 'العربية',
          icon: 'language',
          onPress: handleLanguageToggle,
          showSwitch: true,
          switchValue: language === 'ar',
        },
        {
          title: t('settings.theme'),
          description: theme === 'light' ? t('settings.light') : t('settings.dark'),
          icon: 'brightness-6',
          onPress: handleThemeToggle,
          showSwitch: true,
          switchValue: theme === 'dark',
        },
        {
          title: t('settings.currency'),
          description: currency,
          icon: 'attach-money',
          onPress: () => {},
        },
      ],
    },
    {
      title: t('settings.notifications'),
      items: [
        {
          title: 'Push Notifications',
          description: 'Receive notifications for important updates',
          icon: 'notifications',
          onPress: () => {},
          showSwitch: true,
          switchValue: true,
        },
        {
          title: 'Low Stock Alerts',
          description: 'Get notified when products are running low',
          icon: 'warning',
          onPress: () => {},
          showSwitch: true,
          switchValue: true,
        },
      ],
    },
    {
      title: t('settings.backup'),
      items: [
        {
          title: 'Backup Data',
          description: 'Create a backup of your data',
          icon: 'backup',
          onPress: () => {},
        },
        {
          title: 'Restore Data',
          description: 'Restore data from a backup',
          icon: 'restore',
          onPress: () => {},
        },
        {
          title: 'Export Data',
          description: 'Export your data to CSV/Excel',
          icon: 'file-download',
          onPress: () => {},
        },
      ],
    },
    {
      title: t('settings.about'),
      items: [
        {
          title: t('settings.version'),
          description: '1.0.0',
          icon: 'info',
          onPress: () => {},
        },
        {
          title: t('settings.support'),
          description: 'Get help and support',
          icon: 'help',
          onPress: () => {},
        },
        {
          title: t('settings.privacy'),
          description: 'Privacy policy and terms',
          icon: 'privacy-tip',
          onPress: () => {},
        },
      ],
    },
  ];

  const renderSettingItem = (item: any, index: number) => (
    <View key={index}>
      <List.Item
        title={item.title}
        description={item.description}
        left={(props) => <List.Icon {...props} icon={item.icon} />}
        right={() => 
          item.showSwitch ? (
            <Switch
              value={item.switchValue}
              onValueChange={item.onPress}
              color={colors.primary}
            />
          ) : (
            <List.Icon icon="chevron-right" />
          )
        }
        onPress={!item.showSwitch ? item.onPress : undefined}
        style={[styles.listItem, isRTL && styles.listItemRTL]}
        titleStyle={[styles.listTitle, isRTL && styles.listTitleRTL]}
        descriptionStyle={[styles.listDescription, isRTL && styles.listDescriptionRTL]}
      />
      {index < settingSections[0].items.length - 1 && <Divider />}
    </View>
  );

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title={t('settings.title')} />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        {/* User Profile Section */}
        <Card style={styles.profileCard}>
          <Card.Content>
            <View style={[styles.profileHeader, isRTL && styles.profileHeaderRTL]}>
              <View style={styles.avatarContainer}>
                <Icon name="account-circle" size={60} color={colors.primary} />
              </View>
              <View style={styles.profileInfo}>
                <Text style={[styles.userName, isRTL && styles.userNameRTL]}>
                  {user?.username || 'User'}
                </Text>
                <Text style={[styles.userEmail, isRTL && styles.userEmailRTL]}>
                  {user?.email || '<EMAIL>'}
                </Text>
                <Text style={[styles.userRole, isRTL && styles.userRoleRTL]}>
                  {user?.role || 'Employee'}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Settings Sections */}
        {settingSections.map((section, sectionIndex) => (
          <Card key={sectionIndex} style={styles.settingsCard}>
            <Card.Content>
              <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
                {section.title}
              </Text>
              {section.items.map((item, itemIndex) => renderSettingItem(item, itemIndex))}
            </Card.Content>
          </Card>
        ))}

        {/* Logout Button */}
        <Card style={styles.logoutCard}>
          <Card.Content>
            <Button
              mode="contained"
              onPress={handleLogout}
              icon="logout"
              style={styles.logoutButton}
              buttonColor={colors.error}
              textColor={colors.onError}
            >
              {t('auth.logout')}
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  profileCard: {
    marginBottom: spacing.md,
    borderRadius: 12,
    elevation: 2,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  avatarContainer: {
    marginRight: spacing.md,
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  userNameRTL: {
    textAlign: 'right',
  },
  userEmail: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    marginBottom: spacing.xs,
  },
  userEmailRTL: {
    textAlign: 'right',
  },
  userRole: {
    fontSize: 12,
    color: colors.primary,
    textTransform: 'uppercase',
    fontWeight: 'bold',
  },
  userRoleRTL: {
    textAlign: 'right',
  },
  settingsCard: {
    marginBottom: spacing.md,
    borderRadius: 12,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  sectionTitleRTL: {
    textAlign: 'right',
  },
  listItem: {
    paddingHorizontal: 0,
  },
  listItemRTL: {
    flexDirection: 'row-reverse',
  },
  listTitle: {
    fontSize: 16,
    color: colors.onSurface,
  },
  listTitleRTL: {
    textAlign: 'right',
  },
  listDescription: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
  },
  listDescriptionRTL: {
    textAlign: 'right',
  },
  logoutCard: {
    marginBottom: spacing.xl,
    borderRadius: 12,
    elevation: 2,
  },
  logoutButton: {
    borderRadius: 8,
  },
});

export default SettingsScreen;
