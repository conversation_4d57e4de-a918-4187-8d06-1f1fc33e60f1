import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// Import screens
import InventoryScreen from '../screens/inventory/InventoryScreen';
import AddProductScreen from '../screens/inventory/AddProductScreen';
import CategoriesScreen from '../screens/inventory/CategoriesScreen';
import StockMovementScreen from '../screens/inventory/StockMovementScreen';

export type InventoryStackParamList = {
  InventoryList: undefined;
  ProductDetails: { productId: string };
  AddProduct: undefined;
  EditProduct: { productId: string };
  Categories: undefined;
  Warehouses: undefined;
  StockMovement: undefined;
};

const Stack = createStackNavigator<InventoryStackParamList>();

const InventoryNavigator: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Stack.Navigator
      initialRouteName="InventoryList"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="InventoryList"
        component={InventoryScreen}
        options={{
          title: t('navigation.inventory'),
        }}
      />
      <Stack.Screen
        name="AddProduct"
        component={AddProductScreen}
        options={{
          title: t('inventory.addProduct'),
        }}
      />
      <Stack.Screen
        name="Categories"
        component={CategoriesScreen}
        options={{
          title: t('inventory.categories'),
        }}
      />
      <Stack.Screen
        name="StockMovement"
        component={StockMovementScreen}
        options={{
          title: t('inventory.stockMovements'),
        }}
      />
    </Stack.Navigator>
  );
};

export default InventoryNavigator;
