import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  FAB,
  Searchbar,
  Chip,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchPurchaseOrders } from '../../store/slices/purchaseOrderSlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const PurchaseOrdersScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { purchaseOrders, isLoading } = useSelector((state: RootState) => state.purchaseOrders);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadPurchaseOrders();
  }, []);

  const loadPurchaseOrders = async () => {
    try {
      await dispatch(fetchPurchaseOrders());
    } catch (error) {
      console.error('Failed to load purchase orders:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPurchaseOrders();
    setRefreshing(false);
  };

  const filteredOrders = purchaseOrders.filter(order =>
    order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
    order.supplierName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return colors.secondary;
      case 'approved':
        return colors.primary;
      case 'ordered':
        return colors.tertiary;
      case 'received':
        return colors.tertiary;
      case 'cancelled':
        return colors.error;
      default:
        return colors.onSurfaceVariant;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return t('purchaseOrders.pending');
      case 'approved':
        return t('purchaseOrders.approved');
      case 'ordered':
        return t('purchaseOrders.ordered');
      case 'received':
        return t('purchaseOrders.received');
      case 'cancelled':
        return t('purchaseOrders.cancelled');
      default:
        return status;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const renderOrderItem = ({ item }: { item: any }) => (
    <Card style={styles.orderCard} onPress={() => navigation.navigate('PurchaseOrderDetails', { orderId: item.id })}>
      <Card.Content>
        <View style={[styles.orderHeader, isRTL && styles.orderHeaderRTL]}>
          <View style={styles.orderInfo}>
            <Text style={[styles.orderNumber, isRTL && styles.orderNumberRTL]}>
              {item.orderNumber}
            </Text>
            <Text style={[styles.supplierName, isRTL && styles.supplierNameRTL]}>
              {item.supplierName}
            </Text>
          </View>
          <Chip
            mode="outlined"
            compact
            style={[styles.statusChip, { borderColor: getStatusColor(item.status) }]}
            textStyle={{ color: getStatusColor(item.status) }}
          >
            {getStatusText(item.status)}
          </Chip>
        </View>
        
        <View style={styles.orderDetails}>
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Icon name="calendar-today" size={16} color={colors.onSurfaceVariant} />
            <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
              {formatDate(item.orderDate)}
            </Text>
          </View>
          
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Icon name="attach-money" size={16} color={colors.onSurfaceVariant} />
            <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
              {formatCurrency(item.totalAmount)}
            </Text>
          </View>

          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Icon name="inventory" size={16} color={colors.onSurfaceVariant} />
            <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
              {item.itemsCount} {t('purchaseOrders.items')}
            </Text>
          </View>

          {item.expectedDelivery && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Icon name="local-shipping" size={16} color={colors.onSurfaceVariant} />
              <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
                {t('purchaseOrders.expectedDelivery')}: {formatDate(item.expectedDelivery)}
              </Text>
            </View>
          )}
        </View>
      </Card.Content>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="shopping-cart" size={64} color={colors.onSurfaceVariant} />
      <Text style={[styles.emptyStateText, isRTL && styles.emptyStateTextRTL]}>
        {t('messages.noPurchaseOrders')}
      </Text>
      <Text style={[styles.emptyStateSubtext, isRTL && styles.emptyStateSubtextRTL]}>
        {t('purchaseOrders.createFirstOrder')}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title={t('purchaseOrders.title')} />
        <Appbar.Action icon="filter-list" onPress={() => {}} />
      </Appbar.Header>

      <View style={styles.content}>
        <Searchbar
          placeholder={t('common.search')}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />

        <FlatList
          data={filteredOrders}
          renderItem={renderOrderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <FAB
        icon="plus"
        style={[styles.fab, isRTL && styles.fabRTL]}
        onPress={() => navigation.navigate('CreatePurchaseOrder')}
        label={t('purchaseOrders.createOrder')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  listContent: {
    paddingBottom: 100,
  },
  orderCard: {
    marginBottom: spacing.sm,
    borderRadius: 12,
    elevation: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  orderHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  orderInfo: {
    flex: 1,
  },
  orderNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  orderNumberRTL: {
    textAlign: 'right',
  },
  supplierName: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
  },
  supplierNameRTL: {
    textAlign: 'right',
  },
  statusChip: {
    marginLeft: spacing.sm,
  },
  orderDetails: {
    marginTop: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailRowRTL: {
    flexDirection: 'row-reverse',
  },
  detailText: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    marginLeft: spacing.sm,
    flex: 1,
  },
  detailTextRTL: {
    marginLeft: 0,
    marginRight: spacing.sm,
    textAlign: 'right',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
    marginBottom: spacing.xs,
  },
  emptyStateTextRTL: {
    textAlign: 'right',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
  emptyStateSubtextRTL: {
    textAlign: 'right',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  fabRTL: {
    right: undefined,
    left: 0,
  },
});

export default PurchaseOrdersScreen;
