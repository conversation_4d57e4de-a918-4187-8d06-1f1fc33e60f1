import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  FAB,
  Chip,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { logoutUser } from '../../store/slices/authSlice';
import { fetchProducts } from '../../store/slices/inventorySlice';
import { fetchCustomers } from '../../store/slices/customerSlice';
import { fetchInvoices } from '../../store/slices/invoiceSlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const DashboardScreen: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { products, isLoading: inventoryLoading } = useSelector((state: RootState) => state.inventory);
  const { customers, isLoading: customersLoading } = useSelector((state: RootState) => state.customers);
  const { invoices, isLoading: invoicesLoading } = useSelector((state: RootState) => state.invoices);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [refreshing, setRefreshing] = React.useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      await Promise.all([
        dispatch(fetchProducts()),
        dispatch(fetchCustomers()),
        dispatch(fetchInvoices()),
      ]);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleLogout = () => {
    dispatch(logoutUser());
  };

  const isLoading = inventoryLoading || customersLoading || invoicesLoading;

  // Calculate statistics
  const totalProducts = products.length;
  const totalCustomers = customers.length;
  const totalInvoices = invoices.length;
  const lowStockProducts = products.filter(product => {
    // Assuming we have stock information in the product
    return false; // Placeholder logic
  }).length;

  const stats = [
    {
      title: t('dashboard.totalProducts'),
      value: totalProducts.toString(),
      icon: 'inventory',
      color: colors.primary,
    },
    {
      title: t('dashboard.totalCustomers'),
      value: totalCustomers.toString(),
      icon: 'people',
      color: colors.secondary,
    },
    {
      title: t('dashboard.totalInvoices'),
      value: totalInvoices.toString(),
      icon: 'receipt',
      color: colors.tertiary,
    },
    {
      title: t('dashboard.lowStock'),
      value: lowStockProducts.toString(),
      icon: 'warning',
      color: colors.error,
    },
  ];

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title={t('dashboard.title')} />
        <Appbar.Action icon="logout" onPress={handleLogout} />
      </Appbar.Header>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Welcome Section */}
        <Card style={styles.welcomeCard}>
          <Card.Content>
            <Text style={[styles.welcomeTitle, isRTL && styles.welcomeTitleRTL]}>
              {t('dashboard.welcome')}
            </Text>
            <Text style={[styles.welcomeSubtitle, isRTL && styles.welcomeSubtitleRTL]}>
              {user?.username || 'User'}
            </Text>
          </Card.Content>
        </Card>

        {/* Statistics Grid */}
        <View style={styles.statsGrid}>
          {stats.map((stat, index) => (
            <Card key={index} style={styles.statCard}>
              <Card.Content style={styles.statContent}>
                <View style={[styles.statHeader, isRTL && styles.statHeaderRTL]}>
                  <Icon name={stat.icon} size={24} color={stat.color} />
                  <Text style={[styles.statValue, { color: stat.color }]}>
                    {stat.value}
                  </Text>
                </View>
                <Text style={[styles.statTitle, isRTL && styles.statTitleRTL]}>
                  {stat.title}
                </Text>
              </Card.Content>
            </Card>
          ))}
        </View>

        {/* Quick Actions */}
        <Card style={styles.quickActionsCard}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              Quick Actions
            </Text>
            <View style={styles.quickActionsGrid}>
              <Chip
                icon="add"
                mode="outlined"
                style={styles.quickActionChip}
                onPress={() => {}}
              >
                Add Product
              </Chip>
              <Chip
                icon="person-add"
                mode="outlined"
                style={styles.quickActionChip}
                onPress={() => {}}
              >
                Add Customer
              </Chip>
              <Chip
                icon="receipt-long"
                mode="outlined"
                style={styles.quickActionChip}
                onPress={() => {}}
              >
                New Invoice
              </Chip>
              <Chip
                icon="assessment"
                mode="outlined"
                style={styles.quickActionChip}
                onPress={() => {}}
              >
                View Reports
              </Chip>
            </View>
          </Card.Content>
        </Card>

        {/* Recent Activity */}
        <Card style={styles.recentActivityCard}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('dashboard.recentTransactions')}
            </Text>
            <View style={styles.emptyState}>
              <Icon name="inbox" size={48} color={colors.onSurfaceVariant} />
              <Text style={[styles.emptyStateText, isRTL && styles.emptyStateTextRTL]}>
                {t('messages.noData')}
              </Text>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      <FAB
        icon="add"
        style={[styles.fab, isRTL && styles.fabRTL]}
        onPress={() => {}}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  welcomeCard: {
    marginBottom: spacing.md,
    borderRadius: 12,
    backgroundColor: colors.primaryContainer,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onPrimaryContainer,
    marginBottom: spacing.xs,
  },
  welcomeTitleRTL: {
    textAlign: 'right',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: colors.onPrimaryContainer,
    opacity: 0.8,
  },
  welcomeSubtitleRTL: {
    textAlign: 'right',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  statCard: {
    width: '48%',
    marginBottom: spacing.sm,
    borderRadius: 12,
  },
  statContent: {
    alignItems: 'center',
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  statHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginLeft: spacing.sm,
  },
  statTitle: {
    fontSize: 12,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
  statTitleRTL: {
    textAlign: 'right',
  },
  quickActionsCard: {
    marginBottom: spacing.md,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.md,
  },
  sectionTitleRTL: {
    textAlign: 'right',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionChip: {
    width: '48%',
    marginBottom: spacing.sm,
  },
  recentActivityCard: {
    marginBottom: spacing.xl,
    borderRadius: 12,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.onSurfaceVariant,
    marginTop: spacing.sm,
  },
  emptyStateTextRTL: {
    textAlign: 'right',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  fabRTL: {
    right: undefined,
    left: 0,
  },
});

export default DashboardScreen;
