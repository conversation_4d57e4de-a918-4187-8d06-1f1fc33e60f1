import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  FAB,
  Searchbar,
  Chip,
  Badge,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchInvoices } from '../../store/slices/invoiceSlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const InvoicesScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { invoices, isLoading } = useSelector((state: RootState) => state.invoices);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [searchQuery, setSearchQuery] = React.useState('');
  const [refreshing, setRefreshing] = React.useState(false);

  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = async () => {
    try {
      await dispatch(fetchInvoices());
    } catch (error) {
      console.error('Failed to load invoices:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInvoices();
    setRefreshing(false);
  };

  const filteredInvoices = invoices.filter(invoice =>
    invoice.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
    invoice.customerName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return colors.tertiary;
      case 'pending':
        return colors.secondary;
      case 'overdue':
        return colors.error;
      case 'draft':
        return colors.onSurfaceVariant;
      default:
        return colors.onSurfaceVariant;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return t('invoices.paid');
      case 'pending':
        return t('invoices.pending');
      case 'overdue':
        return t('invoices.overdue');
      case 'draft':
        return t('invoices.draft');
      default:
        return status;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const renderInvoiceItem = ({ item }: { item: any }) => (
    <Card style={styles.invoiceCard} onPress={() => navigation.navigate('InvoiceDetails', { invoiceId: item.id })}>
      <Card.Content>
        <View style={[styles.invoiceHeader, isRTL && styles.invoiceHeaderRTL]}>
          <View style={styles.invoiceInfo}>
            <Text style={[styles.invoiceNumber, isRTL && styles.invoiceNumberRTL]}>
              {item.invoiceNumber}
            </Text>
            <Text style={[styles.customerName, isRTL && styles.customerNameRTL]}>
              {item.customerName}
            </Text>
          </View>
          <Chip
            mode="outlined"
            compact
            style={[styles.statusChip, { borderColor: getStatusColor(item.status) }]}
            textStyle={{ color: getStatusColor(item.status) }}
          >
            {getStatusText(item.status)}
          </Chip>
        </View>
        
        <View style={styles.invoiceDetails}>
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Icon name="calendar-today" size={16} color={colors.onSurfaceVariant} />
            <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
              {formatDate(item.invoiceDate)}
            </Text>
          </View>
          
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Icon name="attach-money" size={16} color={colors.onSurfaceVariant} />
            <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
              {formatCurrency(item.totalAmount)}
            </Text>
          </View>

          {item.dueDate && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Icon name="schedule" size={16} color={colors.onSurfaceVariant} />
              <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
                {t('invoices.dueDate')}: {formatDate(item.dueDate)}
              </Text>
            </View>
          )}
        </View>

        <View style={[styles.invoiceActions, isRTL && styles.invoiceActionsRTL]}>
          <Chip
            icon="print"
            mode="outlined"
            compact
            onPress={() => {}}
            style={styles.actionChip}
          >
            {t('common.print')}
          </Chip>
          <Chip
            icon="share"
            mode="outlined"
            compact
            onPress={() => {}}
            style={styles.actionChip}
          >
            {t('common.share')}
          </Chip>
        </View>
      </Card.Content>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="receipt" size={64} color={colors.onSurfaceVariant} />
      <Text style={[styles.emptyStateText, isRTL && styles.emptyStateTextRTL]}>
        {t('messages.noData')}
      </Text>
      <Text style={[styles.emptyStateSubtext, isRTL && styles.emptyStateSubtextRTL]}>
        {t('invoices.addInvoice')}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title={t('invoices.title')} />
        <Appbar.Action icon="filter-list" onPress={() => {}} />
      </Appbar.Header>

      <View style={styles.content}>
        <Searchbar
          placeholder={t('common.search')}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />

        <FlatList
          data={filteredInvoices}
          renderItem={renderInvoiceItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <FAB
        icon="plus"
        style={[styles.fab, isRTL && styles.fabRTL]}
        onPress={() => navigation.navigate('CreateInvoice')}
        label={t('invoices.addInvoice')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  listContent: {
    paddingBottom: 100,
  },
  invoiceCard: {
    marginBottom: spacing.sm,
    borderRadius: 12,
    elevation: 2,
  },
  invoiceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  invoiceHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  invoiceInfo: {
    flex: 1,
  },
  invoiceNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  invoiceNumberRTL: {
    textAlign: 'right',
  },
  customerName: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
  },
  customerNameRTL: {
    textAlign: 'right',
  },
  statusChip: {
    marginLeft: spacing.sm,
  },
  invoiceDetails: {
    marginVertical: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailRowRTL: {
    flexDirection: 'row-reverse',
  },
  detailText: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    marginLeft: spacing.sm,
    flex: 1,
  },
  detailTextRTL: {
    marginLeft: 0,
    marginRight: spacing.sm,
    textAlign: 'right',
  },
  invoiceActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: spacing.sm,
  },
  invoiceActionsRTL: {
    flexDirection: 'row-reverse',
    justifyContent: 'flex-start',
  },
  actionChip: {
    marginLeft: spacing.sm,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
    marginBottom: spacing.xs,
  },
  emptyStateTextRTL: {
    textAlign: 'right',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
  emptyStateSubtextRTL: {
    textAlign: 'right',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  fabRTL: {
    right: undefined,
    left: 0,
  },
});

export default InvoicesScreen;
