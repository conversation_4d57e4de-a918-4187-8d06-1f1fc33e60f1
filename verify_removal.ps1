# Augment Code Removal Verification Script
# This script checks if Augment Code has been completely removed from your system

param(
    [switch]$Detailed
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Section {
    param([string]$Title)
    Write-Host "`n" + "="*60 -ForegroundColor Blue
    Write-Host $Title -ForegroundColor Blue
    Write-Host "="*60 -ForegroundColor Blue
}

function Test-AugmentPresence {
    param([string]$Path, [string]$Description)
    
    if (Test-Path $Path) {
        Write-ColorOutput "⚠️  FOUND: $Description at $Path" $Red
        return $true
    } else {
        if ($Detailed) {
            Write-ColorOutput "✓ Clean: $Description" $Green
        }
        return $false
    }
}

function Check-AugmentProcesses {
    Write-Section "Checking Running Processes"
    
    $found = $false
    $processes = Get-Process -ErrorAction SilentlyContinue | Where-Object { $_.Name -like "*augment*" }
    
    if ($processes) {
        foreach ($process in $processes) {
            Write-ColorOutput "⚠️  FOUND: Running process $($process.Name) (PID: $($process.Id))" $Red
            $found = $true
        }
    } else {
        Write-ColorOutput "✓ No Augment processes running" $Green
    }
    
    return $found
}

function Check-SystemDirectories {
    Write-Section "Checking System Directories"
    
    $found = $false
    $systemPaths = @(
        "$env:ProgramFiles\Augment",
        "$env:ProgramFiles\Augment Code",
        "$env:ProgramFiles\AugmentCode",
        "${env:ProgramFiles(x86)}\Augment",
        "${env:ProgramFiles(x86)}\Augment Code",
        "${env:ProgramFiles(x86)}\AugmentCode",
        "$env:LOCALAPPDATA\Augment",
        "$env:LOCALAPPDATA\AugmentCode",
        "$env:LOCALAPPDATA\Augment Code",
        "$env:APPDATA\Augment",
        "$env:APPDATA\AugmentCode",
        "$env:APPDATA\Augment Code"
    )
    
    foreach ($path in $systemPaths) {
        if (Test-AugmentPresence -Path $path -Description "System directory") {
            $found = $true
        }
    }
    
    return $found
}

function Check-UserProfile {
    Write-Section "Checking User Profile"
    
    $found = $false
    $userPaths = @(
        "$env:USERPROFILE\.augment",
        "$env:USERPROFILE\.augmentcode",
        "$env:USERPROFILE\AppData\Local\Augment",
        "$env:USERPROFILE\AppData\Local\AugmentCode",
        "$env:USERPROFILE\AppData\Roaming\Augment",
        "$env:USERPROFILE\AppData\Roaming\AugmentCode",
        "$env:USERPROFILE\Documents\Augment",
        "$env:USERPROFILE\Documents\AugmentCode"
    )
    
    foreach ($path in $userPaths) {
        if (Test-AugmentPresence -Path $path -Description "User profile directory") {
            $found = $true
        }
    }
    
    return $found
}

function Check-VSCodeExtensions {
    Write-Section "Checking VS Code Extensions"
    
    $found = $false
    
    # Check extension directories
    if (Test-Path "$env:USERPROFILE\.vscode\extensions") {
        $extensions = Get-ChildItem -Path "$env:USERPROFILE\.vscode\extensions" -Directory | Where-Object { $_.Name -like "*augment*" }
        foreach ($extension in $extensions) {
            Write-ColorOutput "⚠️  FOUND: VS Code extension $($extension.Name)" $Red
            $found = $true
        }
    }
    
    # Check via code command
    if (Get-Command code -ErrorAction SilentlyContinue) {
        try {
            $installedExtensions = & code --list-extensions 2>$null | Where-Object { $_ -like "*augment*" }
            foreach ($ext in $installedExtensions) {
                Write-ColorOutput "⚠️  FOUND: Installed VS Code extension $ext" $Red
                $found = $true
            }
        } catch {
            Write-ColorOutput "Could not check VS Code extensions via CLI" $Yellow
        }
    }
    
    if (-not $found -and $Detailed) {
        Write-ColorOutput "✓ No Augment VS Code extensions found" $Green
    }
    
    return $found
}

function Check-Registry {
    Write-Section "Checking Windows Registry"
    
    $found = $false
    $registryPaths = @(
        "HKCU:\Software\Augment",
        "HKCU:\Software\AugmentCode",
        "HKLM:\Software\Augment",
        "HKLM:\Software\AugmentCode"
    )
    
    foreach ($regPath in $registryPaths) {
        if (Test-Path $regPath) {
            Write-ColorOutput "⚠️  FOUND: Registry key $regPath" $Red
            $found = $true
        }
    }
    
    # Check uninstall keys
    $uninstallPaths = @(
        "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall",
        "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall"
    )
    
    foreach ($uninstallPath in $uninstallPaths) {
        if (Test-Path $uninstallPath) {
            $keys = Get-ChildItem -Path $uninstallPath -ErrorAction SilentlyContinue | Where-Object { $_.Name -like "*Augment*" }
            foreach ($key in $keys) {
                Write-ColorOutput "⚠️  FOUND: Uninstall registry key $($key.Name)" $Red
                $found = $true
            }
        }
    }
    
    if (-not $found -and $Detailed) {
        Write-ColorOutput "✓ No Augment registry entries found" $Green
    }
    
    return $found
}

function Check-EnvironmentVariables {
    Write-Section "Checking Environment Variables"
    
    $found = $false
    $envVars = @("AUGMENT_HOME", "AUGMENT_PATH", "AUGMENT_CONFIG")
    
    foreach ($var in $envVars) {
        $userValue = [Environment]::GetEnvironmentVariable($var, "User")
        $systemValue = [Environment]::GetEnvironmentVariable($var, "Machine")
        
        if ($userValue) {
            Write-ColorOutput "⚠️  FOUND: User environment variable $var = $userValue" $Red
            $found = $true
        }
        
        if ($systemValue) {
            Write-ColorOutput "⚠️  FOUND: System environment variable $var = $systemValue" $Red
            $found = $true
        }
    }
    
    # Check PATH
    $userPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    $systemPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    
    if ($userPath -and ($userPath -like "*augment*" -or $userPath -like "*Augment*")) {
        Write-ColorOutput "⚠️  FOUND: Augment in user PATH" $Red
        $found = $true
    }
    
    if ($systemPath -and ($systemPath -like "*augment*" -or $systemPath -like "*Augment*")) {
        Write-ColorOutput "⚠️  FOUND: Augment in system PATH" $Red
        $found = $true
    }
    
    if (-not $found -and $Detailed) {
        Write-ColorOutput "✓ No Augment environment variables found" $Green
    }
    
    return $found
}

function Check-ProjectFiles {
    Write-Section "Checking Project Files"
    
    $found = $false
    $projectDirs = @(
        "$env:USERPROFILE\Documents",
        "$env:USERPROFILE\Desktop",
        "$env:USERPROFILE\source",
        "$env:USERPROFILE\repos",
        "$env:USERPROFILE\projects"
    )
    
    $augmentFiles = @(
        ".augment",
        ".augmentcode",
        "augment.json",
        "augment.config.js",
        "augment.config.json",
        ".augment.json"
    )
    
    foreach ($dir in $projectDirs) {
        if (Test-Path $dir) {
            foreach ($filePattern in $augmentFiles) {
                $files = Get-ChildItem -Path $dir -Include $filePattern -Recurse -Force -ErrorAction SilentlyContinue
                foreach ($file in $files) {
                    Write-ColorOutput "⚠️  FOUND: Project file $($file.FullName)" $Red
                    $found = $true
                }
            }
        }
    }
    
    if (-not $found -and $Detailed) {
        Write-ColorOutput "✓ No Augment project files found" $Green
    }
    
    return $found
}

function Check-NPMPackages {
    Write-Section "Checking NPM Global Packages"
    
    $found = $false
    
    if (Get-Command npm -ErrorAction SilentlyContinue) {
        try {
            $globalPackages = & npm list -g --depth=0 --json 2>$null | ConvertFrom-Json
            if ($globalPackages.dependencies) {
                $augmentPackages = $globalPackages.dependencies.PSObject.Properties | Where-Object { $_.Name -like "*augment*" }
                foreach ($package in $augmentPackages) {
                    Write-ColorOutput "⚠️  FOUND: NPM global package $($package.Name)" $Red
                    $found = $true
                }
            }
        } catch {
            Write-ColorOutput "Could not check NPM packages: $($_.Exception.Message)" $Yellow
        }
    } else {
        if ($Detailed) {
            Write-ColorOutput "NPM not found - skipping package check" $Yellow
        }
    }
    
    if (-not $found -and $Detailed) {
        Write-ColorOutput "✓ No Augment NPM packages found" $Green
    }
    
    return $found
}

# Main execution
Write-ColorOutput "Augment Code Removal Verification" $Blue
Write-ColorOutput "=================================" $Blue

$totalIssues = 0

# Run all checks
$totalIssues += if (Check-AugmentProcesses) { 1 } else { 0 }
$totalIssues += if (Check-SystemDirectories) { 1 } else { 0 }
$totalIssues += if (Check-UserProfile) { 1 } else { 0 }
$totalIssues += if (Check-VSCodeExtensions) { 1 } else { 0 }
$totalIssues += if (Check-Registry) { 1 } else { 0 }
$totalIssues += if (Check-EnvironmentVariables) { 1 } else { 0 }
$totalIssues += if (Check-ProjectFiles) { 1 } else { 0 }
$totalIssues += if (Check-NPMPackages) { 1 } else { 0 }

# Summary
Write-Section "Verification Summary"

if ($totalIssues -eq 0) {
    Write-ColorOutput "🎉 SUCCESS: Augment Code appears to be completely removed!" $Green
    Write-ColorOutput "No traces of Augment Code were found on your system." $Green
} else {
    Write-ColorOutput "⚠️  WARNING: $totalIssues issue(s) found!" $Red
    Write-ColorOutput "Some Augment Code components may still be present." $Red
    Write-ColorOutput "Consider running the removal script again or manually removing the found items." $Yellow
}

Write-ColorOutput "`nFor detailed output, run: .\verify_removal.ps1 -Detailed" $Blue
