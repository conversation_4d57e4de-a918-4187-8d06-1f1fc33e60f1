{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "loading": "Loading...", "error": "Error", "success": "Success", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "close": "Close", "open": "Open", "view": "View", "print": "Print", "export": "Export", "import": "Import", "share": "Share", "copy": "Copy", "paste": "Paste", "cut": "Cut", "select": "Select", "selectAll": "Select All", "refresh": "Refresh", "update": "Update", "create": "Create", "new": "New", "total": "Total", "subtotal": "Subtotal", "quantity": "Quantity", "price": "Price", "amount": "Amount", "date": "Date", "time": "Time", "name": "Name", "description": "Description", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "actions": "Actions", "generatePDF": "Generate PDF"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "email": "Email", "register": "Register", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginError": "Invalid username or password", "logoutSuccess": "Logout successful", "registerSuccess": "Registration successful", "registerError": "Registration failed", "welcomeBack": "Welcome back!", "pleaseLogin": "Please login to continue", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?"}, "navigation": {"dashboard": "Dashboard", "inventory": "Inventory", "products": "Products", "categories": "Categories", "warehouses": "Warehouses", "stock": "Stock", "customers": "Customers", "suppliers": "Suppliers", "sales": "Sales", "invoices": "Invoices", "purchases": "Purchases", "purchaseOrders": "Purchase Orders", "reports": "Reports", "settings": "Settings", "profile": "Profile", "help": "Help", "about": "About"}, "whatsapp": {"sendMessage": "Send WhatsApp", "sendInvoice": "Send Invoice via WhatsApp", "sendPurchaseOrder": "Send PO via WhatsApp", "sendCustomerInfo": "Send Customer Info", "sendStockAlert": "Send Stock <PERSON>", "defaultMessage": "Hello! This message was sent from our Inventory Management App."}, "financial": {"dashboard": "Financial Dashboard", "summary": "Financial Summary", "totalRevenue": "Total Revenue", "totalExpenses": "Total Expenses", "netProfit": "Net Profit", "totalSales": "Total Sales", "period": "Period", "thisWeek": "This Week", "thisMonth": "This Month", "thisQuarter": "This Quarter", "thisYear": "This Year", "quickActions": "Quick Actions", "addTransaction": "Add Transaction", "viewReports": "View Reports", "allTransactions": "All Transactions", "categories": "Categories", "recentTransactions": "Recent Transactions"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to Inventory Management", "totalProducts": "Total Products", "totalCustomers": "Total Customers", "totalInvoices": "Total Invoices", "totalRevenue": "Total Revenue", "lowStock": "Low Stock Items", "recentTransactions": "Recent Transactions", "topProducts": "Top Products", "salesChart": "Sales Chart", "inventoryValue": "Inventory Value", "pendingOrders": "Pending Orders"}, "inventory": {"title": "Inventory Management", "products": "Products", "addProduct": "Add Product", "editProduct": "Edit Product", "productName": "Product Name", "productCode": "Product Code", "sku": "SKU", "barcode": "Barcode", "category": "Category", "unitPrice": "Unit Price", "costPrice": "Cost Price", "stockLevel": "Stock Level", "minStock": "Minimum Stock", "maxStock": "Maximum Stock", "unit": "Unit", "warehouse": "Warehouse", "inStock": "In Stock", "outOfStock": "Out of Stock", "lowStock": "Low Stock", "stockMovement": "Stock Movement", "stockAdjustment": "Stock Adjustment", "transferStock": "Transfer Stock", "receiveStock": "Receive Stock", "issueStock": "Issue Stock", "cartons": "Cartons", "pieces": "Pieces", "piecesPerCarton": "Pieces per Carton"}, "customers": {"title": "Customer Management", "addCustomer": "Add Customer", "editCustomer": "Edit Customer", "customerName": "Customer Name", "customerType": "Customer Type", "individual": "Individual", "business": "Business", "phone": "Phone", "whatsapp": "WhatsApp", "address": "Address", "city": "City", "country": "Country", "taxNumber": "Tax Number", "creditLimit": "Credit Limit", "paymentTerms": "Payment Terms", "notes": "Notes", "contactInfo": "Contact Information", "billingAddress": "Billing Address", "customerDetails": "Customer Details"}, "invoices": {"title": "Invoice Management", "addInvoice": "Add Invoice", "editInvoice": "Edit Invoice", "invoiceNumber": "Invoice Number", "invoiceDate": "Invoice Date", "dueDate": "Due Date", "customer": "Customer", "items": "Items", "addItem": "Add Item", "removeItem": "Remove Item", "taxAmount": "Tax Amount", "discountAmount": "Discount Amount", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "balanceAmount": "Balance Amount", "paymentStatus": "Payment Status", "pending": "Pending", "partial": "Partial", "paid": "Paid", "overdue": "Overdue", "draft": "Draft", "sent": "<PERSON><PERSON>", "cancelled": "Cancelled", "generatePDF": "Generate PDF", "sendWhatsApp": "Send via WhatsApp", "markAsPaid": "<PERSON> as <PERSON><PERSON>", "printInvoice": "Print Invoice"}, "reports": {"title": "Reports", "salesReport": "Sales Report", "inventoryReport": "Inventory Report", "customerReport": "Customer Report", "financialReport": "Financial Report", "stockReport": "Stock Report", "profitLoss": "Profit & Loss", "balanceSheet": "Balance Sheet", "cashFlow": "Cash Flow", "dateRange": "Date Range", "fromDate": "From Date", "toDate": "To Date", "generateReport": "Generate Report", "exportPDF": "Export PDF", "exportExcel": "Export Excel"}, "settings": {"title": "Settings", "general": "General Settings", "language": "Language", "theme": "Theme", "currency": "<PERSON><PERSON><PERSON><PERSON>", "dateFormat": "Date Format", "timeFormat": "Time Format", "notifications": "Notifications", "backup": "Backup & Restore", "security": "Security", "about": "About", "version": "Version", "support": "Support", "privacy": "Privacy Policy", "terms": "Terms of Service", "light": "Light", "dark": "Dark", "english": "English", "arabic": "العربية"}, "validation": {"required": "This field is required", "email": "Please enter a valid email", "phone": "Please enter a valid phone number", "number": "Please enter a valid number", "positive": "Please enter a positive number", "minLength": "Minimum length is {{min}} characters", "maxLength": "Maximum length is {{max}} characters", "min": "Minimum value is {{min}}", "max": "Maximum value is {{max}}", "unique": "This value must be unique", "match": "Values do not match"}, "messages": {"saveSuccess": "Saved successfully", "saveError": "Failed to save", "deleteSuccess": "Deleted successfully", "deleteError": "Failed to delete", "updateSuccess": "Updated successfully", "updateError": "Failed to update", "createSuccess": "Created successfully", "createError": "Failed to create", "loadError": "Failed to load data", "networkError": "Network error occurred", "confirmDelete": "Are you sure you want to delete this item?", "noData": "No data available", "searchNoResults": "No results found", "operationSuccess": "Operation completed successfully", "operationError": "Operation failed"}}