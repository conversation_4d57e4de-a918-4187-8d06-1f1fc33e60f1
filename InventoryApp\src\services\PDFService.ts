import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

export interface PDFOptions {
  title: string;
  filename: string;
  isRTL?: boolean;
  orientation?: 'portrait' | 'landscape';
  margins?: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

export interface InvoicePDFData {
  invoiceNumber: string;
  date: string;
  dueDate: string;
  customer: {
    name: string;
    email?: string;
    phone?: string;
    address?: string;
  };
  company: {
    name: string;
    address?: string;
    phone?: string;
    email?: string;
    logo?: string;
  };
  items: Array<{
    productName: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  total: number;
  notes?: string;
  status: string;
}

export interface PurchaseOrderPDFData {
  orderNumber: string;
  orderDate: string;
  expectedDelivery: string;
  supplier: {
    name: string;
    email?: string;
    phone?: string;
    address?: string;
  };
  company: {
    name: string;
    address?: string;
    phone?: string;
    email?: string;
    logo?: string;
  };
  items: Array<{
    productName: string;
    quantity: number;
    unitCost: number;
    total: number;
  }>;
  subtotal: number;
  taxAmount: number;
  shippingCost: number;
  totalAmount: number;
  notes?: string;
  status: string;
}

class PDFService {
  private static instance: PDFService;

  public static getInstance(): PDFService {
    if (!PDFService.instance) {
      PDFService.instance = new PDFService();
    }
    return PDFService.instance;
  }

  /**
   * Generate invoice PDF
   */
  async generateInvoicePDF(data: InvoicePDFData, options: PDFOptions): Promise<string> {
    try {
      const html = this.generateInvoiceHTML(data, options.isRTL);
      
      const { uri } = await Print.printToFileAsync({
        html,
        base64: false,
        margins: options.margins || {
          top: 20,
          bottom: 20,
          left: 20,
          right: 20,
        },
      });

      // Move to a permanent location
      const filename = `${options.filename}.pdf`;
      const permanentUri = `${FileSystem.documentDirectory}${filename}`;
      await FileSystem.moveAsync({
        from: uri,
        to: permanentUri,
      });

      return permanentUri;
    } catch (error) {
      console.error('Error generating invoice PDF:', error);
      throw new Error('Failed to generate invoice PDF');
    }
  }

  /**
   * Generate purchase order PDF
   */
  async generatePurchaseOrderPDF(data: PurchaseOrderPDFData, options: PDFOptions): Promise<string> {
    try {
      const html = this.generatePurchaseOrderHTML(data, options.isRTL);
      
      const { uri } = await Print.printToFileAsync({
        html,
        base64: false,
        margins: options.margins || {
          top: 20,
          bottom: 20,
          left: 20,
          right: 20,
        },
      });

      // Move to a permanent location
      const filename = `${options.filename}.pdf`;
      const permanentUri = `${FileSystem.documentDirectory}${filename}`;
      await FileSystem.moveAsync({
        from: uri,
        to: permanentUri,
      });

      return permanentUri;
    } catch (error) {
      console.error('Error generating purchase order PDF:', error);
      throw new Error('Failed to generate purchase order PDF');
    }
  }

  /**
   * Generate financial report PDF
   */
  async generateFinancialReportPDF(data: any, options: PDFOptions): Promise<string> {
    try {
      const html = this.generateFinancialReportHTML(data, options.isRTL);
      
      const { uri } = await Print.printToFileAsync({
        html,
        base64: false,
        margins: options.margins || {
          top: 20,
          bottom: 20,
          left: 20,
          right: 20,
        },
      });

      // Move to a permanent location
      const filename = `${options.filename}.pdf`;
      const permanentUri = `${FileSystem.documentDirectory}${filename}`;
      await FileSystem.moveAsync({
        from: uri,
        to: permanentUri,
      });

      return permanentUri;
    } catch (error) {
      console.error('Error generating financial report PDF:', error);
      throw new Error('Failed to generate financial report PDF');
    }
  }

  /**
   * Share PDF file
   */
  async sharePDF(uri: string, title: string): Promise<void> {
    try {
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: title,
          UTI: 'com.adobe.pdf'
        });
      } else {
        Alert.alert('Error', 'Sharing is not available on this device');
      }
    } catch (error) {
      console.error('Error sharing PDF:', error);
      Alert.alert('Error', 'Failed to share PDF file');
    }
  }

  /**
   * Print PDF file
   */
  async printPDF(uri: string): Promise<void> {
    try {
      await Print.printAsync({
        uri,
      });
    } catch (error) {
      console.error('Error printing PDF:', error);
      Alert.alert('Error', 'Failed to print PDF file');
    }
  }

  /**
   * Generate invoice HTML template
   */
  private generateInvoiceHTML(data: InvoicePDFData, isRTL: boolean = false): string {
    const direction = isRTL ? 'rtl' : 'ltr';
    const textAlign = isRTL ? 'right' : 'left';
    const reverseAlign = isRTL ? 'left' : 'right';

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);
    };

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString();
    };

    return `
<!DOCTYPE html>
<html dir="${direction}">
<head>
    <meta charset="utf-8">
    <title>Invoice ${data.invoiceNumber}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            direction: ${direction};
            color: #333;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 20px;
        }
        .company-info {
            text-align: ${textAlign};
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 5px;
        }
        .invoice-info {
            text-align: ${reverseAlign};
        }
        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 10px;
        }
        .invoice-number {
            font-size: 18px;
            margin-bottom: 5px;
        }
        .billing-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .billing-info {
            width: 45%;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: ${textAlign};
        }
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #2196F3;
        }
        .items-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .totals-section {
            width: 300px;
            margin-${reverseAlign}: 0;
            margin-${textAlign}: auto;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .total-row.final {
            font-weight: bold;
            font-size: 18px;
            color: #2196F3;
            border-bottom: 2px solid #2196F3;
            border-top: 2px solid #2196F3;
            margin-top: 10px;
            padding-top: 10px;
        }
        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
        }
        .status.paid { background-color: #4CAF50; color: white; }
        .status.pending { background-color: #FF9800; color: white; }
        .status.overdue { background-color: #F44336; color: white; }
        .status.draft { background-color: #9E9E9E; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-info">
            <div class="company-name">${data.company.name}</div>
            ${data.company.address ? `<div>${data.company.address}</div>` : ''}
            ${data.company.phone ? `<div>Phone: ${data.company.phone}</div>` : ''}
            ${data.company.email ? `<div>Email: ${data.company.email}</div>` : ''}
        </div>
        <div class="invoice-info">
            <div class="invoice-title">${isRTL ? 'فاتورة' : 'INVOICE'}</div>
            <div class="invoice-number">#${data.invoiceNumber}</div>
            <div class="status ${data.status}">${data.status}</div>
        </div>
    </div>

    <div class="billing-section">
        <div class="billing-info">
            <div class="section-title">${isRTL ? 'الفاتورة إلى:' : 'Bill To:'}</div>
            <div><strong>${data.customer.name}</strong></div>
            ${data.customer.email ? `<div>${data.customer.email}</div>` : ''}
            ${data.customer.phone ? `<div>${data.customer.phone}</div>` : ''}
            ${data.customer.address ? `<div>${data.customer.address}</div>` : ''}
        </div>
        <div class="billing-info">
            <div class="section-title">${isRTL ? 'تفاصيل الفاتورة:' : 'Invoice Details:'}</div>
            <div><strong>${isRTL ? 'تاريخ الفاتورة:' : 'Invoice Date:'}</strong> ${formatDate(data.date)}</div>
            <div><strong>${isRTL ? 'تاريخ الاستحقاق:' : 'Due Date:'}</strong> ${formatDate(data.dueDate)}</div>
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>${isRTL ? 'المنتج' : 'Product'}</th>
                <th>${isRTL ? 'الكمية' : 'Quantity'}</th>
                <th>${isRTL ? 'سعر الوحدة' : 'Unit Price'}</th>
                <th>${isRTL ? 'المجموع' : 'Total'}</th>
            </tr>
        </thead>
        <tbody>
            ${data.items.map(item => `
                <tr>
                    <td>${item.productName}</td>
                    <td>${item.quantity}</td>
                    <td>${formatCurrency(item.unitPrice)}</td>
                    <td>${formatCurrency(item.total)}</td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="totals-section">
        <div class="total-row">
            <span>${isRTL ? 'المجموع الفرعي:' : 'Subtotal:'}</span>
            <span>${formatCurrency(data.subtotal)}</span>
        </div>
        ${data.discountAmount > 0 ? `
        <div class="total-row">
            <span>${isRTL ? 'الخصم:' : 'Discount:'}</span>
            <span>-${formatCurrency(data.discountAmount)}</span>
        </div>
        ` : ''}
        <div class="total-row">
            <span>${isRTL ? 'الضريبة:' : 'Tax:'}</span>
            <span>${formatCurrency(data.taxAmount)}</span>
        </div>
        <div class="total-row final">
            <span>${isRTL ? 'المجموع الكلي:' : 'Total:'}</span>
            <span>${formatCurrency(data.total)}</span>
        </div>
    </div>

    ${data.notes ? `
    <div class="notes">
        <div class="section-title">${isRTL ? 'ملاحظات:' : 'Notes:'}</div>
        <p>${data.notes}</p>
    </div>
    ` : ''}

    <div class="footer">
        <p>${isRTL ? 'شكراً لك على عملك معنا!' : 'Thank you for your business!'}</p>
        <p>${isRTL ? 'تم إنشاؤها بواسطة تطبيق إدارة المخزون' : 'Generated by Inventory Management App'}</p>
    </div>
</body>
</html>
    `;
  }

  /**
   * Generate purchase order HTML template
   */
  private generatePurchaseOrderHTML(data: PurchaseOrderPDFData, isRTL: boolean = false): string {
    const direction = isRTL ? 'rtl' : 'ltr';
    const textAlign = isRTL ? 'right' : 'left';
    const reverseAlign = isRTL ? 'left' : 'right';

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);
    };

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString();
    };

    return `
<!DOCTYPE html>
<html dir="${direction}">
<head>
    <meta charset="utf-8">
    <title>Purchase Order ${data.orderNumber}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            direction: ${direction};
            color: #333;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 20px;
        }
        .company-info {
            text-align: ${textAlign};
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        .po-info {
            text-align: ${reverseAlign};
        }
        .po-title {
            font-size: 28px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
        }
        .po-number {
            font-size: 18px;
            margin-bottom: 5px;
        }
        .supplier-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .supplier-info {
            width: 45%;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: ${textAlign};
        }
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #4CAF50;
        }
        .items-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .totals-section {
            width: 300px;
            margin-${reverseAlign}: 0;
            margin-${textAlign}: auto;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .total-row.final {
            font-weight: bold;
            font-size: 18px;
            color: #4CAF50;
            border-bottom: 2px solid #4CAF50;
            border-top: 2px solid #4CAF50;
            margin-top: 10px;
            padding-top: 10px;
        }
        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
        }
        .status.pending { background-color: #FF9800; color: white; }
        .status.approved { background-color: #2196F3; color: white; }
        .status.ordered { background-color: #9C27B0; color: white; }
        .status.received { background-color: #4CAF50; color: white; }
        .status.cancelled { background-color: #F44336; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-info">
            <div class="company-name">${data.company.name}</div>
            ${data.company.address ? `<div>${data.company.address}</div>` : ''}
            ${data.company.phone ? `<div>Phone: ${data.company.phone}</div>` : ''}
            ${data.company.email ? `<div>Email: ${data.company.email}</div>` : ''}
        </div>
        <div class="po-info">
            <div class="po-title">${isRTL ? 'أمر شراء' : 'PURCHASE ORDER'}</div>
            <div class="po-number">#${data.orderNumber}</div>
            <div class="status ${data.status}">${data.status}</div>
        </div>
    </div>

    <div class="supplier-section">
        <div class="supplier-info">
            <div class="section-title">${isRTL ? 'المورد:' : 'Supplier:'}</div>
            <div><strong>${data.supplier.name}</strong></div>
            ${data.supplier.email ? `<div>${data.supplier.email}</div>` : ''}
            ${data.supplier.phone ? `<div>${data.supplier.phone}</div>` : ''}
            ${data.supplier.address ? `<div>${data.supplier.address}</div>` : ''}
        </div>
        <div class="supplier-info">
            <div class="section-title">${isRTL ? 'تفاصيل الطلب:' : 'Order Details:'}</div>
            <div><strong>${isRTL ? 'تاريخ الطلب:' : 'Order Date:'}</strong> ${formatDate(data.orderDate)}</div>
            <div><strong>${isRTL ? 'التسليم المتوقع:' : 'Expected Delivery:'}</strong> ${formatDate(data.expectedDelivery)}</div>
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>${isRTL ? 'المنتج' : 'Product'}</th>
                <th>${isRTL ? 'الكمية' : 'Quantity'}</th>
                <th>${isRTL ? 'تكلفة الوحدة' : 'Unit Cost'}</th>
                <th>${isRTL ? 'المجموع' : 'Total'}</th>
            </tr>
        </thead>
        <tbody>
            ${data.items.map(item => `
                <tr>
                    <td>${item.productName}</td>
                    <td>${item.quantity}</td>
                    <td>${formatCurrency(item.unitCost)}</td>
                    <td>${formatCurrency(item.total)}</td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="totals-section">
        <div class="total-row">
            <span>${isRTL ? 'المجموع الفرعي:' : 'Subtotal:'}</span>
            <span>${formatCurrency(data.subtotal)}</span>
        </div>
        <div class="total-row">
            <span>${isRTL ? 'الضريبة:' : 'Tax:'}</span>
            <span>${formatCurrency(data.taxAmount)}</span>
        </div>
        <div class="total-row">
            <span>${isRTL ? 'تكلفة الشحن:' : 'Shipping:'}</span>
            <span>${formatCurrency(data.shippingCost)}</span>
        </div>
        <div class="total-row final">
            <span>${isRTL ? 'المجموع الكلي:' : 'Total:'}</span>
            <span>${formatCurrency(data.totalAmount)}</span>
        </div>
    </div>

    ${data.notes ? `
    <div class="notes">
        <div class="section-title">${isRTL ? 'ملاحظات:' : 'Notes:'}</div>
        <p>${data.notes}</p>
    </div>
    ` : ''}

    <div class="footer">
        <p>${isRTL ? 'يرجى تأكيد استلام هذا الطلب والجدول الزمني للتسليم المتوقع.' : 'Please confirm receipt of this order and expected delivery timeline.'}</p>
        <p>${isRTL ? 'تم إنشاؤها بواسطة تطبيق إدارة المخزون' : 'Generated by Inventory Management App'}</p>
    </div>
</body>
</html>
    `;
  }

  /**
   * Generate financial report HTML template
   */
  private generateFinancialReportHTML(data: any, isRTL: boolean = false): string {
    // Implementation for financial report HTML
    // This would be similar to the above templates but for financial data
    return `
<!DOCTYPE html>
<html dir="${isRTL ? 'rtl' : 'ltr'}">
<head>
    <meta charset="utf-8">
    <title>Financial Report</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            direction: ${isRTL ? 'rtl' : 'ltr'};
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #FF9800;
            padding-bottom: 20px;
        }
        .report-title {
            font-size: 28px;
            font-weight: bold;
            color: #FF9800;
            margin-bottom: 10px;
        }
        .report-period {
            font-size: 16px;
            color: #666;
        }
        .summary-section {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .summary-item {
            text-align: center;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin: 10px;
            min-width: 200px;
        }
        .summary-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .summary-value {
            font-size: 24px;
            font-weight: bold;
            color: #FF9800;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="report-title">${isRTL ? 'التقرير المالي' : 'Financial Report'}</div>
        <div class="report-period">${data.period || ''}</div>
    </div>

    <div class="summary-section">
        <div class="summary-item">
            <div class="summary-label">${isRTL ? 'إجمالي الإيرادات' : 'Total Revenue'}</div>
            <div class="summary-value">$${(data.totalRevenue || 0).toLocaleString()}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">${isRTL ? 'إجمالي المصروفات' : 'Total Expenses'}</div>
            <div class="summary-value">$${(data.totalExpenses || 0).toLocaleString()}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">${isRTL ? 'صافي الربح' : 'Net Profit'}</div>
            <div class="summary-value">$${((data.totalRevenue || 0) - (data.totalExpenses || 0)).toLocaleString()}</div>
        </div>
    </div>

    <div class="footer">
        <p>${isRTL ? 'تم إنشاؤها بواسطة تطبيق إدارة المخزون' : 'Generated by Inventory Management App'}</p>
        <p>${new Date().toLocaleDateString()}</p>
    </div>
</body>
</html>
    `;
  }
}

export default PDFService;
