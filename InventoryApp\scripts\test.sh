#!/bin/bash

# Inventory Management App - Test Runner Script
# This script runs comprehensive tests for the application

echo "🧪 Starting Inventory Management App Tests..."
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_warning "node_modules not found. Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install dependencies"
        exit 1
    fi
fi

# Run different types of tests
run_unit_tests() {
    print_status "Running unit tests..."
    npm test -- --coverage --watchAll=false
    if [ $? -eq 0 ]; then
        print_success "Unit tests passed!"
        return 0
    else
        print_error "Unit tests failed!"
        return 1
    fi
}

run_integration_tests() {
    print_status "Running integration tests..."
    npm test -- --testPathPattern="integration" --watchAll=false
    if [ $? -eq 0 ]; then
        print_success "Integration tests passed!"
        return 0
    else
        print_error "Integration tests failed!"
        return 1
    fi
}

run_e2e_tests() {
    print_status "Running end-to-end tests..."
    # Note: E2E tests would require additional setup with Detox or similar
    print_warning "E2E tests not implemented yet. Skipping..."
    return 0
}

lint_code() {
    print_status "Running code linting..."
    npm run lint
    if [ $? -eq 0 ]; then
        print_success "Code linting passed!"
        return 0
    else
        print_error "Code linting failed!"
        return 1
    fi
}

type_check() {
    print_status "Running TypeScript type checking..."
    npx tsc --noEmit
    if [ $? -eq 0 ]; then
        print_success "Type checking passed!"
        return 0
    else
        print_error "Type checking failed!"
        return 1
    fi
}

# Parse command line arguments
UNIT_TESTS=true
INTEGRATION_TESTS=true
E2E_TESTS=false
LINT=true
TYPE_CHECK=true
COVERAGE=true

while [[ $# -gt 0 ]]; do
    case $1 in
        --unit-only)
            INTEGRATION_TESTS=false
            E2E_TESTS=false
            LINT=false
            TYPE_CHECK=false
            shift
            ;;
        --integration-only)
            UNIT_TESTS=false
            E2E_TESTS=false
            LINT=false
            TYPE_CHECK=false
            shift
            ;;
        --e2e-only)
            UNIT_TESTS=false
            INTEGRATION_TESTS=false
            LINT=false
            TYPE_CHECK=false
            E2E_TESTS=true
            shift
            ;;
        --no-lint)
            LINT=false
            shift
            ;;
        --no-type-check)
            TYPE_CHECK=false
            shift
            ;;
        --no-coverage)
            COVERAGE=false
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --unit-only      Run only unit tests"
            echo "  --integration-only Run only integration tests"
            echo "  --e2e-only       Run only end-to-end tests"
            echo "  --no-lint        Skip code linting"
            echo "  --no-type-check  Skip TypeScript type checking"
            echo "  --no-coverage    Skip coverage report"
            echo "  --help           Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Track test results
FAILED_TESTS=()

# Run TypeScript type checking
if [ "$TYPE_CHECK" = true ]; then
    if ! type_check; then
        FAILED_TESTS+=("TypeScript type checking")
    fi
fi

# Run code linting
if [ "$LINT" = true ]; then
    if ! lint_code; then
        FAILED_TESTS+=("Code linting")
    fi
fi

# Run unit tests
if [ "$UNIT_TESTS" = true ]; then
    if ! run_unit_tests; then
        FAILED_TESTS+=("Unit tests")
    fi
fi

# Run integration tests
if [ "$INTEGRATION_TESTS" = true ]; then
    if ! run_integration_tests; then
        FAILED_TESTS+=("Integration tests")
    fi
fi

# Run E2E tests
if [ "$E2E_TESTS" = true ]; then
    if ! run_e2e_tests; then
        FAILED_TESTS+=("End-to-end tests")
    fi
fi

# Generate test report
echo ""
echo "=============================================="
echo "🏁 Test Results Summary"
echo "=============================================="

if [ ${#FAILED_TESTS[@]} -eq 0 ]; then
    print_success "All tests passed! ✅"
    
    # Show coverage report if available
    if [ "$COVERAGE" = true ] && [ -f "coverage/lcov-report/index.html" ]; then
        print_status "Coverage report generated at: coverage/lcov-report/index.html"
    fi
    
    exit 0
else
    print_error "The following tests failed:"
    for test in "${FAILED_TESTS[@]}"; do
        echo "  ❌ $test"
    done
    
    echo ""
    print_error "Please fix the failing tests before proceeding."
    exit 1
fi
