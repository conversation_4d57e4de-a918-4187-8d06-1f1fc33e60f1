import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product, Category, Warehouse, Stock, ProductForm } from '../../types';
import DatabaseService from '../../database/DatabaseService';

interface InventoryState {
  products: Product[];
  categories: Category[];
  warehouses: Warehouse[];
  stock: Stock[];
  selectedWarehouse: string | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: InventoryState = {
  products: [],
  categories: [],
  warehouses: [],
  stock: [],
  selectedWarehouse: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchProducts = createAsyncThunk(
  'inventory/fetchProducts',
  async (_, { rejectWithValue }) => {
    try {
      const products = await DatabaseService.getProducts();
      return products;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch products');
    }
  }
);

export const createProduct = createAsyncThunk(
  'inventory/createProduct',
  async (productData: ProductForm, { rejectWithValue }) => {
    try {
      const productId = await DatabaseService.createProduct(productData);
      const products = await DatabaseService.getProducts();
      return products;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create product');
    }
  }
);

export const updateProduct = createAsyncThunk(
  'inventory/updateProduct',
  async ({ id, productData }: { id: string; productData: ProductForm }, { rejectWithValue }) => {
    try {
      await DatabaseService.updateProduct(id, productData);
      const products = await DatabaseService.getProducts();
      return products;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update product');
    }
  }
);

export const fetchCategories = createAsyncThunk(
  'inventory/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const categories = await DatabaseService.getCategories();
      return categories;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch categories');
    }
  }
);

export const createCategory = createAsyncThunk(
  'inventory/createCategory',
  async (categoryData: any, { rejectWithValue }) => {
    try {
      await DatabaseService.createCategory(categoryData);
      const categories = await DatabaseService.getCategories();
      return categories;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create category');
    }
  }
);

export const fetchWarehouses = createAsyncThunk(
  'inventory/fetchWarehouses',
  async (_, { rejectWithValue }) => {
    try {
      const warehouses = await DatabaseService.getWarehouses();
      return warehouses;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch warehouses');
    }
  }
);

export const createWarehouse = createAsyncThunk(
  'inventory/createWarehouse',
  async (warehouseData: any, { rejectWithValue }) => {
    try {
      await DatabaseService.createWarehouse(warehouseData);
      const warehouses = await DatabaseService.getWarehouses();
      return warehouses;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create warehouse');
    }
  }
);

export const fetchStock = createAsyncThunk(
  'inventory/fetchStock',
  async (warehouseId?: string, { rejectWithValue }) => {
    try {
      const stock = await DatabaseService.getStock(warehouseId);
      return stock;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch stock');
    }
  }
);

export const updateStock = createAsyncThunk(
  'inventory/updateStock',
  async (
    { productId, warehouseId, quantity, cartonQuantity }: 
    { productId: string; warehouseId: string; quantity: number; cartonQuantity?: number },
    { rejectWithValue }
  ) => {
    try {
      await DatabaseService.updateStock(productId, warehouseId, quantity, cartonQuantity || 0);
      const stock = await DatabaseService.getStock();
      return stock;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update stock');
    }
  }
);

const inventorySlice = createSlice({
  name: 'inventory',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedWarehouse: (state, action: PayloadAction<string | null>) => {
      state.selectedWarehouse = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch products
      .addCase(fetchProducts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.products = action.payload;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Create product
      .addCase(createProduct.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.products = action.payload;
      })
      .addCase(createProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update product
      .addCase(updateProduct.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.products = action.payload;
      })
      .addCase(updateProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch categories
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.categories = action.payload;
      })
      .addCase(createCategory.fulfilled, (state, action) => {
        state.categories = action.payload;
      })
      // Fetch warehouses
      .addCase(fetchWarehouses.fulfilled, (state, action) => {
        state.warehouses = action.payload;
        if (action.payload.length > 0 && !state.selectedWarehouse) {
          state.selectedWarehouse = action.payload[0].id;
        }
      })
      .addCase(createWarehouse.fulfilled, (state, action) => {
        state.warehouses = action.payload;
      })
      // Fetch stock
      .addCase(fetchStock.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchStock.fulfilled, (state, action) => {
        state.isLoading = false;
        state.stock = action.payload;
      })
      .addCase(fetchStock.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update stock
      .addCase(updateStock.fulfilled, (state, action) => {
        state.stock = action.payload;
      });
  },
});

export const { clearError, setSelectedWarehouse, setLoading } = inventorySlice.actions;
export default inventorySlice.reducer;
