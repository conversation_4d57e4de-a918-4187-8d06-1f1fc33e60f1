import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { DatabaseService } from '../../database/DatabaseService';

export interface Transaction {
  id: string;
  type: 'income' | 'expense' | 'sale' | 'purchase' | 'transfer';
  category: string;
  amount: number;
  description: string;
  date: string;
  referenceType?: string;
  referenceId?: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface FinancialSummary {
  totalRevenue: number;
  totalExpenses: number;
  totalSales: number;
  totalPurchases: number;
  netProfit: number;
  period: string;
}

export interface FinancialCategory {
  id: string;
  name: string;
  nameAr: string;
  type: 'income' | 'expense';
  color: string;
  icon: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface FinancialState {
  transactions: Transaction[];
  categories: FinancialCategory[];
  summary: FinancialSummary | null;
  recentTransactions: Transaction[];
  isLoading: boolean;
  error: string | null;
}

const initialState: FinancialState = {
  transactions: [],
  categories: [],
  summary: null,
  recentTransactions: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchTransactions = createAsyncThunk(
  'financial/fetchTransactions',
  async (filters?: { startDate?: string; endDate?: string; type?: string; category?: string }) => {
    const db = DatabaseService.getInstance();
    return await db.getTransactions(filters?.startDate, filters?.endDate);
  }
);

export const fetchRecentTransactions = createAsyncThunk(
  'financial/fetchRecentTransactions',
  async () => {
    const db = DatabaseService.getInstance();
    return await db.getRecentTransactions(10);
  }
);

export const fetchFinancialSummary = createAsyncThunk(
  'financial/fetchSummary',
  async (period: string) => {
    const db = DatabaseService.getInstance();
    return await db.getFinancialSummary(period);
  }
);

export const createTransaction = createAsyncThunk(
  'financial/createTransaction',
  async (transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>) => {
    const db = DatabaseService.getInstance();
    const id = await db.createTransaction(transactionData);
    return { ...transactionData, id };
  }
);

export const updateTransaction = createAsyncThunk(
  'financial/updateTransaction',
  async ({ id, ...updateData }: Partial<Transaction> & { id: string }) => {
    const db = DatabaseService.getInstance();
    await db.updateTransaction(id, updateData);
    return { id, ...updateData };
  }
);

export const deleteTransaction = createAsyncThunk(
  'financial/deleteTransaction',
  async (id: string) => {
    const db = DatabaseService.getInstance();
    await db.deleteTransaction(id);
    return id;
  }
);

export const fetchFinancialCategories = createAsyncThunk(
  'financial/fetchCategories',
  async () => {
    const db = DatabaseService.getInstance();
    return await db.getFinancialCategories();
  }
);

export const createFinancialCategory = createAsyncThunk(
  'financial/createCategory',
  async (categoryData: Omit<FinancialCategory, 'id' | 'createdAt' | 'updatedAt'>) => {
    const db = DatabaseService.getInstance();
    const id = await db.createFinancialCategory(categoryData);
    return { ...categoryData, id };
  }
);

export const updateFinancialCategory = createAsyncThunk(
  'financial/updateCategory',
  async ({ id, ...updateData }: Partial<FinancialCategory> & { id: string }) => {
    const db = DatabaseService.getInstance();
    await db.updateFinancialCategory(id, updateData);
    return { id, ...updateData };
  }
);

export const deleteFinancialCategory = createAsyncThunk(
  'financial/deleteCategory',
  async (id: string) => {
    const db = DatabaseService.getInstance();
    await db.deleteFinancialCategory(id);
    return id;
  }
);

const financialSlice = createSlice({
  name: 'financial',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setTransactions: (state, action: PayloadAction<Transaction[]>) => {
      state.transactions = action.payload;
    },
    setCategories: (state, action: PayloadAction<FinancialCategory[]>) => {
      state.categories = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch transactions
      .addCase(fetchTransactions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.transactions = action.payload;
      })
      .addCase(fetchTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch transactions';
      })

      // Fetch recent transactions
      .addCase(fetchRecentTransactions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchRecentTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.recentTransactions = action.payload;
      })
      .addCase(fetchRecentTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch recent transactions';
      })

      // Fetch financial summary
      .addCase(fetchFinancialSummary.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFinancialSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.summary = action.payload;
      })
      .addCase(fetchFinancialSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch financial summary';
      })

      // Create transaction
      .addCase(createTransaction.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createTransaction.fulfilled, (state, action) => {
        state.isLoading = false;
        const newTransaction: Transaction = {
          ...action.payload,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        state.transactions.unshift(newTransaction);
        state.recentTransactions.unshift(newTransaction);
        if (state.recentTransactions.length > 10) {
          state.recentTransactions = state.recentTransactions.slice(0, 10);
        }
      })
      .addCase(createTransaction.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to create transaction';
      })

      // Update transaction
      .addCase(updateTransaction.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateTransaction.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.transactions.findIndex(t => t.id === action.payload.id);
        if (index >= 0) {
          state.transactions[index] = {
            ...state.transactions[index],
            ...action.payload,
            updatedAt: new Date().toISOString(),
          };
        }
        const recentIndex = state.recentTransactions.findIndex(t => t.id === action.payload.id);
        if (recentIndex >= 0) {
          state.recentTransactions[recentIndex] = {
            ...state.recentTransactions[recentIndex],
            ...action.payload,
            updatedAt: new Date().toISOString(),
          };
        }
      })
      .addCase(updateTransaction.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update transaction';
      })

      // Delete transaction
      .addCase(deleteTransaction.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteTransaction.fulfilled, (state, action) => {
        state.isLoading = false;
        state.transactions = state.transactions.filter(t => t.id !== action.payload);
        state.recentTransactions = state.recentTransactions.filter(t => t.id !== action.payload);
      })
      .addCase(deleteTransaction.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to delete transaction';
      })

      // Fetch categories
      .addCase(fetchFinancialCategories.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFinancialCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = action.payload;
      })
      .addCase(fetchFinancialCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch categories';
      })

      // Create category
      .addCase(createFinancialCategory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createFinancialCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        const newCategory: FinancialCategory = {
          ...action.payload,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        state.categories.push(newCategory);
      })
      .addCase(createFinancialCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to create category';
      })

      // Update category
      .addCase(updateFinancialCategory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateFinancialCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.categories.findIndex(c => c.id === action.payload.id);
        if (index >= 0) {
          state.categories[index] = {
            ...state.categories[index],
            ...action.payload,
            updatedAt: new Date().toISOString(),
          };
        }
      })
      .addCase(updateFinancialCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update category';
      })

      // Delete category
      .addCase(deleteFinancialCategory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteFinancialCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = state.categories.filter(c => c.id !== action.payload);
      })
      .addCase(deleteFinancialCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to delete category';
      });
  },
});

export const { clearError, setTransactions, setCategories } = financialSlice.actions;
export default financialSlice.reducer;
