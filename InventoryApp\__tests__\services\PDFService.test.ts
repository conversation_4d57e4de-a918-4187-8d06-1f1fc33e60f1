import PDFService, { InvoicePDFData, PurchaseOrderPDFData, PDFOptions } from '../../src/services/PDFService';

// Mock Expo Print and Sharing
jest.mock('expo-print', () => ({
  printToFileAsync: jest.fn(() => Promise.resolve({ uri: 'file://test.pdf' })),
  printAsync: jest.fn(() => Promise.resolve()),
}));

jest.mock('expo-sharing', () => ({
  shareAsync: jest.fn(() => Promise.resolve()),
  isAvailableAsync: jest.fn(() => Promise.resolve(true)),
}));

jest.mock('expo-file-system', () => ({
  documentDirectory: 'file://documents/',
  moveAsync: jest.fn(() => Promise.resolve()),
}));

describe('PDFService', () => {
  let pdfService: PDFService;

  beforeEach(() => {
    pdfService = PDFService.getInstance();
    jest.clearAllMocks();
  });

  describe('Singleton Pattern', () => {
    test('should return the same instance', () => {
      const instance1 = PDFService.getInstance();
      const instance2 = PDFService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Invoice PDF Generation', () => {
    const mockInvoiceData: InvoicePDFData = {
      invoiceNumber: 'INV-001',
      date: '2024-01-15',
      dueDate: '2024-02-15',
      customer: {
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Customer Street',
      },
      company: {
        name: 'Test Company',
        address: '456 Company Ave',
        phone: '+0987654321',
        email: '<EMAIL>',
      },
      items: [
        {
          productName: 'Test Product 1',
          quantity: 2,
          unitPrice: 100,
          total: 200,
        },
        {
          productName: 'Test Product 2',
          quantity: 1,
          unitPrice: 150,
          total: 150,
        },
      ],
      subtotal: 350,
      taxAmount: 35,
      discountAmount: 10,
      total: 375,
      notes: 'Test invoice notes',
      status: 'paid',
    };

    const mockOptions: PDFOptions = {
      title: 'Test Invoice',
      filename: 'test-invoice',
      isRTL: false,
    };

    test('should generate invoice PDF successfully', async () => {
      const result = await pdfService.generateInvoicePDF(mockInvoiceData, mockOptions);
      expect(result).toBe('file://documents/test-invoice.pdf');
    });

    test('should generate RTL invoice PDF', async () => {
      const rtlOptions = { ...mockOptions, isRTL: true };
      const result = await pdfService.generateInvoicePDF(mockInvoiceData, rtlOptions);
      expect(result).toBe('file://documents/test-invoice.pdf');
    });

    test('should handle invoice PDF generation errors', async () => {
      const mockError = new Error('PDF generation failed');
      const { printToFileAsync } = require('expo-print');
      printToFileAsync.mockRejectedValueOnce(mockError);

      await expect(
        pdfService.generateInvoicePDF(mockInvoiceData, mockOptions)
      ).rejects.toThrow('Failed to generate invoice PDF');
    });
  });

  describe('Purchase Order PDF Generation', () => {
    const mockPOData: PurchaseOrderPDFData = {
      orderNumber: 'PO-001',
      orderDate: '2024-01-15',
      expectedDelivery: '2024-01-22',
      supplier: {
        name: 'Test Supplier',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: '789 Supplier Road',
      },
      company: {
        name: 'Test Company',
        address: '456 Company Ave',
        phone: '+0987654321',
        email: '<EMAIL>',
      },
      items: [
        {
          productName: 'Raw Material 1',
          quantity: 100,
          unitCost: 10,
          total: 1000,
        },
        {
          productName: 'Raw Material 2',
          quantity: 50,
          unitCost: 20,
          total: 1000,
        },
      ],
      subtotal: 2000,
      taxAmount: 200,
      shippingCost: 50,
      totalAmount: 2250,
      notes: 'Test purchase order notes',
      status: 'approved',
    };

    const mockOptions: PDFOptions = {
      title: 'Test Purchase Order',
      filename: 'test-po',
      isRTL: false,
    };

    test('should generate purchase order PDF successfully', async () => {
      const result = await pdfService.generatePurchaseOrderPDF(mockPOData, mockOptions);
      expect(result).toBe('file://documents/test-po.pdf');
    });

    test('should generate RTL purchase order PDF', async () => {
      const rtlOptions = { ...mockOptions, isRTL: true };
      const result = await pdfService.generatePurchaseOrderPDF(mockPOData, rtlOptions);
      expect(result).toBe('file://documents/test-po.pdf');
    });

    test('should handle purchase order PDF generation errors', async () => {
      const mockError = new Error('PDF generation failed');
      const { printToFileAsync } = require('expo-print');
      printToFileAsync.mockRejectedValueOnce(mockError);

      await expect(
        pdfService.generatePurchaseOrderPDF(mockPOData, mockOptions)
      ).rejects.toThrow('Failed to generate purchase order PDF');
    });
  });

  describe('Financial Report PDF Generation', () => {
    const mockFinancialData = {
      period: 'January 2024',
      totalRevenue: 50000,
      totalExpenses: 30000,
      netProfit: 20000,
    };

    const mockOptions: PDFOptions = {
      title: 'Financial Report',
      filename: 'financial-report',
      isRTL: false,
    };

    test('should generate financial report PDF successfully', async () => {
      const result = await pdfService.generateFinancialReportPDF(mockFinancialData, mockOptions);
      expect(result).toBe('file://documents/financial-report.pdf');
    });

    test('should generate RTL financial report PDF', async () => {
      const rtlOptions = { ...mockOptions, isRTL: true };
      const result = await pdfService.generateFinancialReportPDF(mockFinancialData, rtlOptions);
      expect(result).toBe('file://documents/financial-report.pdf');
    });
  });

  describe('PDF Sharing and Printing', () => {
    const testUri = 'file://test.pdf';
    const testTitle = 'Test Document';

    test('should share PDF successfully', async () => {
      await pdfService.sharePDF(testUri, testTitle);
      const { shareAsync } = require('expo-sharing');
      expect(shareAsync).toHaveBeenCalledWith(testUri, {
        mimeType: 'application/pdf',
        dialogTitle: testTitle,
        UTI: 'com.adobe.pdf',
      });
    });

    test('should handle sharing unavailable', async () => {
      const { isAvailableAsync } = require('expo-sharing');
      isAvailableAsync.mockResolvedValueOnce(false);

      // Should not throw error, but show alert
      await expect(pdfService.sharePDF(testUri, testTitle)).resolves.not.toThrow();
    });

    test('should print PDF successfully', async () => {
      await pdfService.printPDF(testUri);
      const { printAsync } = require('expo-print');
      expect(printAsync).toHaveBeenCalledWith({ uri: testUri });
    });

    test('should handle print errors', async () => {
      const mockError = new Error('Print failed');
      const { printAsync } = require('expo-print');
      printAsync.mockRejectedValueOnce(mockError);

      // Should not throw error, but show alert
      await expect(pdfService.printPDF(testUri)).resolves.not.toThrow();
    });
  });

  describe('HTML Generation', () => {
    test('should generate valid HTML for invoice', async () => {
      const mockInvoiceData: InvoicePDFData = {
        invoiceNumber: 'INV-001',
        date: '2024-01-15',
        dueDate: '2024-02-15',
        customer: {
          name: 'Test Customer',
          email: '<EMAIL>',
        },
        company: {
          name: 'Test Company',
        },
        items: [],
        subtotal: 0,
        taxAmount: 0,
        discountAmount: 0,
        total: 0,
        status: 'draft',
      };

      const mockOptions: PDFOptions = {
        title: 'Test Invoice',
        filename: 'test-invoice',
        isRTL: false,
      };

      // This should not throw an error
      await expect(
        pdfService.generateInvoicePDF(mockInvoiceData, mockOptions)
      ).resolves.toBeDefined();
    });

    test('should handle empty items array', async () => {
      const mockInvoiceData: InvoicePDFData = {
        invoiceNumber: 'INV-001',
        date: '2024-01-15',
        dueDate: '2024-02-15',
        customer: { name: 'Test Customer' },
        company: { name: 'Test Company' },
        items: [], // Empty items
        subtotal: 0,
        taxAmount: 0,
        discountAmount: 0,
        total: 0,
        status: 'draft',
      };

      const mockOptions: PDFOptions = {
        title: 'Test Invoice',
        filename: 'test-invoice',
        isRTL: false,
      };

      await expect(
        pdfService.generateInvoicePDF(mockInvoiceData, mockOptions)
      ).resolves.toBeDefined();
    });
  });

  describe('Custom Margins and Options', () => {
    test('should use custom margins', async () => {
      const mockInvoiceData: InvoicePDFData = {
        invoiceNumber: 'INV-001',
        date: '2024-01-15',
        dueDate: '2024-02-15',
        customer: { name: 'Test Customer' },
        company: { name: 'Test Company' },
        items: [],
        subtotal: 0,
        taxAmount: 0,
        discountAmount: 0,
        total: 0,
        status: 'draft',
      };

      const customOptions: PDFOptions = {
        title: 'Test Invoice',
        filename: 'test-invoice',
        isRTL: false,
        margins: {
          top: 30,
          bottom: 30,
          left: 25,
          right: 25,
        },
      };

      const result = await pdfService.generateInvoicePDF(mockInvoiceData, customOptions);
      expect(result).toBeDefined();

      const { printToFileAsync } = require('expo-print');
      expect(printToFileAsync).toHaveBeenCalledWith(
        expect.objectContaining({
          margins: customOptions.margins,
        })
      );
    });
  });
});
