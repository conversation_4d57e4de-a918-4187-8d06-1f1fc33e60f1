# Testing Guide for Inventory Management App

This document provides comprehensive information about testing the Inventory Management App.

## Table of Contents

1. [Test Structure](#test-structure)
2. [Running Tests](#running-tests)
3. [Test Types](#test-types)
4. [Coverage Reports](#coverage-reports)
5. [Writing Tests](#writing-tests)
6. [Continuous Integration](#continuous-integration)
7. [Troubleshooting](#troubleshooting)

## Test Structure

```
__tests__/
├── components/           # Component tests
│   ├── WhatsAppButton.test.tsx
│   └── ...
├── services/            # Service layer tests
│   ├── DatabaseService.test.ts
│   ├── PDFService.test.ts
│   ├── WhatsAppService.test.ts
│   └── ...
├── screens/             # Screen component tests
│   └── ...
├── utils/               # Utility function tests
│   └── ...
└── integration/         # Integration tests
    └── ...
```

## Running Tests

### Prerequisites

Ensure you have all dependencies installed:

```bash
npm install
```

### Basic Test Commands

```bash
# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm test -- --watch

# Run specific test file
npm test DatabaseService.test.ts

# Run tests matching pattern
npm test -- --testNamePattern="should create"
```

### Using the Test Script

We provide a comprehensive test script for different testing scenarios:

```bash
# Run all tests (default)
./scripts/test.sh

# Run only unit tests
./scripts/test.sh --unit-only

# Run only integration tests
./scripts/test.sh --integration-only

# Skip linting
./scripts/test.sh --no-lint

# Skip TypeScript checking
./scripts/test.sh --no-type-check

# Get help
./scripts/test.sh --help
```

## Test Types

### 1. Unit Tests

Test individual components, functions, and services in isolation.

**Example: Testing a service method**

```typescript
describe('DatabaseService', () => {
  test('should create a new product', async () => {
    const productData = {
      name: 'Test Product',
      unitPrice: 100,
      // ... other fields
    };

    const result = await databaseService.createProduct(productData);
    expect(result).toBeDefined();
    expect(typeof result).toBe('string');
  });
});
```

### 2. Component Tests

Test React Native components using React Native Testing Library.

**Example: Testing a button component**

```typescript
import { render, fireEvent } from '@testing-library/react-native';

test('should handle button press', () => {
  const onPress = jest.fn();
  const { getByText } = render(
    <CustomButton onPress={onPress} title="Test Button" />
  );

  fireEvent.press(getByText('Test Button'));
  expect(onPress).toHaveBeenCalled();
});
```

### 3. Integration Tests

Test how different parts of the application work together.

**Example: Testing database and service integration**

```typescript
describe('Invoice Integration', () => {
  test('should create invoice and update inventory', async () => {
    // Create product
    const productId = await databaseService.createProduct(productData);
    
    // Create invoice with that product
    const invoiceData = {
      items: [{ productId, quantity: 2 }],
      // ... other fields
    };
    
    const invoiceId = await databaseService.createInvoice(invoiceData);
    
    // Verify inventory was updated
    const product = await databaseService.getProductById(productId);
    expect(product.stockLevel).toBe(originalStock - 2);
  });
});
```

### 4. Snapshot Tests

Capture component output and detect unexpected changes.

```typescript
test('should match snapshot', () => {
  const { toJSON } = render(<ProductCard product={mockProduct} />);
  expect(toJSON()).toMatchSnapshot();
});
```

## Coverage Reports

### Generating Coverage

```bash
# Generate coverage report
npm test -- --coverage

# Generate coverage with specific threshold
npm test -- --coverage --coverageThreshold='{"global":{"branches":80,"functions":80,"lines":80,"statements":80}}'
```

### Coverage Reports Location

- **HTML Report**: `coverage/lcov-report/index.html`
- **Text Report**: Displayed in terminal
- **LCOV Report**: `coverage/lcov.info`

### Coverage Targets

We aim for the following coverage targets:

- **Statements**: 80%
- **Branches**: 75%
- **Functions**: 80%
- **Lines**: 80%

## Writing Tests

### Best Practices

1. **Descriptive Test Names**: Use clear, descriptive names that explain what is being tested.

```typescript
// Good
test('should create invoice with correct total when tax is applied')

// Bad
test('invoice creation')
```

2. **Arrange, Act, Assert Pattern**:

```typescript
test('should calculate total with tax', () => {
  // Arrange
  const subtotal = 100;
  const taxRate = 0.1;
  
  // Act
  const total = calculateTotal(subtotal, taxRate);
  
  // Assert
  expect(total).toBe(110);
});
```

3. **Mock External Dependencies**:

```typescript
jest.mock('../../src/services/WhatsAppService');
```

4. **Test Edge Cases**:

```typescript
describe('Product validation', () => {
  test('should handle empty product name', () => {
    expect(() => validateProduct({ name: '' })).toThrow();
  });
  
  test('should handle negative price', () => {
    expect(() => validateProduct({ price: -10 })).toThrow();
  });
});
```

### Testing Async Code

```typescript
test('should fetch products from database', async () => {
  const products = await databaseService.getAllProducts();
  expect(Array.isArray(products)).toBe(true);
});

// Or using resolves/rejects
test('should reject invalid product data', () => {
  return expect(databaseService.createProduct({})).rejects.toThrow();
});
```

### Testing React Components with Redux

```typescript
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

const renderWithRedux = (component, initialState = {}) => {
  const store = configureStore({
    reducer: rootReducer,
    preloadedState: initialState,
  });
  
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};
```

### Testing Navigation

```typescript
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

test('should navigate to product details', () => {
  const { getByText } = render(<ProductList />);
  fireEvent.press(getByText('Product 1'));
  expect(mockNavigate).toHaveBeenCalledWith('ProductDetails', { id: '1' });
});
```

## Continuous Integration

### GitHub Actions Example

```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test -- --coverage --watchAll=false
    
    - name: Upload coverage
      uses: codecov/codecov-action@v2
```

## Troubleshooting

### Common Issues

1. **Metro bundler conflicts**:
   ```bash
   npx react-native start --reset-cache
   ```

2. **Jest cache issues**:
   ```bash
   npm test -- --clearCache
   ```

3. **Mock not working**:
   - Ensure mocks are in the correct location
   - Check mock is called before the module is imported

4. **Async test timeouts**:
   ```typescript
   jest.setTimeout(10000); // Increase timeout
   ```

5. **React Native Testing Library issues**:
   ```bash
   npm install --save-dev @testing-library/react-native
   ```

### Debug Mode

Run tests in debug mode:

```bash
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Verbose Output

Get detailed test output:

```bash
npm test -- --verbose
```

## Test Data Management

### Mock Data

Create reusable mock data:

```typescript
// __tests__/mocks/mockData.ts
export const mockProduct = {
  id: '1',
  name: 'Test Product',
  price: 100,
  // ... other fields
};

export const mockCustomer = {
  id: '1',
  name: 'Test Customer',
  email: '<EMAIL>',
  // ... other fields
};
```

### Database Seeding for Tests

```typescript
beforeEach(async () => {
  await databaseService.clearAllTables();
  await databaseService.seedTestData();
});
```

## Performance Testing

### Testing Component Performance

```typescript
import { measureRenders } from '@testing-library/react-native';

test('should render efficiently', () => {
  const { rerender } = render(<ProductList products={[]} />);
  
  const renderCount = measureRenders(() => {
    rerender(<ProductList products={largeProductList} />);
  });
  
  expect(renderCount).toBeLessThan(5);
});
```

## Accessibility Testing

```typescript
import { getByA11yLabel } from '@testing-library/react-native';

test('should be accessible', () => {
  const { getByA11yLabel } = render(<ProductCard product={mockProduct} />);
  expect(getByA11yLabel('Product name')).toBeTruthy();
});
```

---

For more information about testing React Native applications, refer to:
- [React Native Testing Library Documentation](https://callstack.github.io/react-native-testing-library/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Testing React Native Apps](https://reactnative.dev/docs/testing-overview)
