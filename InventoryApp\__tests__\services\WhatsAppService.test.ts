import WhatsAppService, { WhatsAppMessage, WhatsAppContact } from '../../src/services/WhatsAppService';
import { Linking, Alert, Share } from 'react-native';

// Mock React Native modules
jest.mock('react-native', () => ({
  Linking: {
    canOpenURL: jest.fn(),
    openURL: jest.fn(),
  },
  Alert: {
    alert: jest.fn(),
  },
  Share: {
    share: jest.fn(),
  },
}));

jest.mock('expo-sharing', () => ({
  shareAsync: jest.fn(() => Promise.resolve()),
  isAvailableAsync: jest.fn(() => Promise.resolve(true)),
}));

describe('WhatsAppService', () => {
  let whatsAppService: WhatsAppService;

  beforeEach(() => {
    whatsAppService = WhatsAppService.getInstance();
    jest.clearAllMocks();
  });

  describe('Singleton Pattern', () => {
    test('should return the same instance', () => {
      const instance1 = WhatsAppService.getInstance();
      const instance2 = WhatsAppService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('WhatsApp Availability Check', () => {
    test('should check if WhatsApp is installed', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      
      const isInstalled = await whatsAppService.isWhatsAppInstalled();
      expect(isInstalled).toBe(true);
      expect(Linking.canOpenURL).toHaveBeenCalledWith('whatsapp://send');
    });

    test('should handle WhatsApp not installed', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(false);
      
      const isInstalled = await whatsAppService.isWhatsAppInstalled();
      expect(isInstalled).toBe(false);
    });

    test('should handle availability check errors', async () => {
      (Linking.canOpenURL as jest.Mock).mockRejectedValueOnce(new Error('Check failed'));
      
      const isInstalled = await whatsAppService.isWhatsAppInstalled();
      expect(isInstalled).toBe(false);
    });
  });

  describe('Phone Number Formatting', () => {
    test('should format 10-digit US number', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      await whatsAppService.sendMessage('1234567890', 'Test message');
      
      expect(Linking.openURL).toHaveBeenCalledWith(
        'whatsapp://send?phone=11234567890&text=Test%20message'
      );
    });

    test('should handle 11-digit number with country code', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      await whatsAppService.sendMessage('11234567890', 'Test message');
      
      expect(Linking.openURL).toHaveBeenCalledWith(
        'whatsapp://send?phone=11234567890&text=Test%20message'
      );
    });

    test('should clean phone number with special characters', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      await whatsAppService.sendMessage('+****************', 'Test message');
      
      expect(Linking.openURL).toHaveBeenCalledWith(
        'whatsapp://send?phone=12345678900&text=Test%20message'
      );
    });
  });

  describe('Basic Message Sending', () => {
    test('should send message successfully', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      const result = await whatsAppService.sendMessage('1234567890', 'Hello World');
      
      expect(result).toBe(true);
      expect(Linking.openURL).toHaveBeenCalledWith(
        'whatsapp://send?phone=11234567890&text=Hello%20World'
      );
    });

    test('should handle WhatsApp not installed', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(false);

      const result = await whatsAppService.sendMessage('1234567890', 'Hello World');
      
      expect(result).toBe(false);
      expect(Alert.alert).toHaveBeenCalledWith(
        'WhatsApp Not Found',
        'WhatsApp is not installed on this device. Please install WhatsApp to send messages.',
        expect.any(Array)
      );
    });

    test('should handle message sending errors', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockRejectedValueOnce(new Error('Failed to open'));

      const result = await whatsAppService.sendMessage('1234567890', 'Hello World');
      
      expect(result).toBe(false);
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to send WhatsApp message. Please try again.');
    });

    test('should encode special characters in message', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      await whatsAppService.sendMessage('1234567890', 'Hello & Welcome! 50% off');
      
      expect(Linking.openURL).toHaveBeenCalledWith(
        'whatsapp://send?phone=11234567890&text=Hello%20%26%20Welcome!%2050%25%20off'
      );
    });
  });

  describe('Invoice Sending', () => {
    const mockContact: WhatsAppContact = {
      name: 'John Doe',
      phone: '1234567890',
      email: '<EMAIL>',
    };

    const mockInvoice = {
      invoiceNumber: 'INV-001',
      date: '2024-01-15',
      dueDate: '2024-02-15',
      total: 1000,
      status: 'paid',
      items: [
        { productName: 'Product 1', quantity: 2, total: 500 },
        { productName: 'Product 2', quantity: 1, total: 500 },
      ],
      notes: 'Thank you for your business',
    };

    test('should send invoice without document', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      const result = await whatsAppService.sendInvoice(mockContact, mockInvoice);
      
      expect(result).toBe(true);
      expect(Linking.openURL).toHaveBeenCalled();
    });

    test('should send invoice with document', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);
      
      const { shareAsync, isAvailableAsync } = require('expo-sharing');
      isAvailableAsync.mockResolvedValueOnce(true);
      shareAsync.mockResolvedValueOnce(true);

      const documentUri = 'file://invoice.pdf';
      const result = await whatsAppService.sendInvoice(mockContact, mockInvoice, documentUri);
      
      expect(result).toBe(true);
      expect(Linking.openURL).toHaveBeenCalled();
      expect(shareAsync).toHaveBeenCalledWith(documentUri, {
        mimeType: 'application/pdf',
        dialogTitle: 'Share Invoice_INV-001.pdf',
        UTI: 'com.adobe.pdf',
      });
    });

    test('should handle invoice sending errors', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockRejectedValueOnce(new Error('Failed'));

      const result = await whatsAppService.sendInvoice(mockContact, mockInvoice);
      
      expect(result).toBe(false);
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to send invoice via WhatsApp. Please try again.');
    });
  });

  describe('Purchase Order Sending', () => {
    const mockContact: WhatsAppContact = {
      name: 'Supplier Inc',
      phone: '1234567890',
      email: '<EMAIL>',
    };

    const mockPurchaseOrder = {
      orderNumber: 'PO-001',
      orderDate: '2024-01-15',
      expectedDelivery: '2024-01-22',
      totalAmount: 2000,
      status: 'approved',
      items: [
        { productName: 'Raw Material 1', quantity: 100, total: 1000 },
        { productName: 'Raw Material 2', quantity: 50, total: 1000 },
      ],
      notes: 'Please confirm delivery timeline',
    };

    test('should send purchase order successfully', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      const result = await whatsAppService.sendPurchaseOrder(mockContact, mockPurchaseOrder);
      
      expect(result).toBe(true);
      expect(Linking.openURL).toHaveBeenCalled();
    });

    test('should send purchase order with document', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);
      
      const { shareAsync, isAvailableAsync } = require('expo-sharing');
      isAvailableAsync.mockResolvedValueOnce(true);
      shareAsync.mockResolvedValueOnce(true);

      const documentUri = 'file://po.pdf';
      const result = await whatsAppService.sendPurchaseOrder(mockContact, mockPurchaseOrder, documentUri);
      
      expect(result).toBe(true);
      expect(shareAsync).toHaveBeenCalledWith(documentUri, {
        mimeType: 'application/pdf',
        dialogTitle: 'Share PO_PO-001.pdf',
        UTI: 'com.adobe.pdf',
      });
    });
  });

  describe('Customer Info Sending', () => {
    const mockContact: WhatsAppContact = {
      name: 'Sales Team',
      phone: '1234567890',
    };

    const mockCustomer = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      type: 'Individual',
      address: '123 Main St',
      notes: 'VIP Customer',
    };

    test('should send customer info successfully', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      const result = await whatsAppService.sendCustomerInfo(mockContact, mockCustomer);
      
      expect(result).toBe(true);
      expect(Linking.openURL).toHaveBeenCalled();
    });
  });

  describe('Stock Alert Sending', () => {
    const mockContact: WhatsAppContact = {
      name: 'Inventory Manager',
      phone: '1234567890',
    };

    const mockProducts = [
      { name: 'Product A', currentStock: 5, minStock: 10 },
      { name: 'Product B', currentStock: 2, minStock: 15 },
    ];

    test('should send stock alert successfully', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      const result = await whatsAppService.sendStockAlert(mockContact, mockProducts);
      
      expect(result).toBe(true);
      expect(Linking.openURL).toHaveBeenCalled();
    });

    test('should handle empty products array', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      const result = await whatsAppService.sendStockAlert(mockContact, []);
      
      expect(result).toBe(true);
    });
  });

  describe('Message Template Generation', () => {
    test('should generate proper invoice message format', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);

      const mockContact: WhatsAppContact = {
        name: 'John Doe',
        phone: '1234567890',
      };

      const mockInvoice = {
        invoiceNumber: 'INV-001',
        date: '2024-01-15',
        dueDate: '2024-02-15',
        total: 1000,
        status: 'paid',
        items: [],
      };

      await whatsAppService.sendInvoice(mockContact, mockInvoice);
      
      // Check that the URL contains properly formatted message
      const callArgs = (Linking.openURL as jest.Mock).mock.calls[0][0];
      expect(callArgs).toContain('INV-001');
      expect(callArgs).toContain('John%20Doe');
      expect(callArgs).toContain('1%2C000.00'); // Formatted currency
    });
  });

  describe('Document Sharing Fallback', () => {
    test('should use Share API when Sharing is not available', async () => {
      (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(true);
      (Linking.openURL as jest.Mock).mockResolvedValueOnce(true);
      
      const { isAvailableAsync } = require('expo-sharing');
      isAvailableAsync.mockResolvedValueOnce(false);

      const mockContact: WhatsAppContact = {
        name: 'John Doe',
        phone: '1234567890',
      };

      const mockInvoice = {
        invoiceNumber: 'INV-001',
        total: 1000,
        status: 'paid',
      };

      const documentUri = 'file://invoice.pdf';
      const result = await whatsAppService.sendInvoice(mockContact, mockInvoice, documentUri);
      
      expect(result).toBe(true);
      expect(Share.share).toHaveBeenCalledWith({
        url: documentUri,
        title: 'Invoice_INV-001.pdf',
        message: 'Please find attached: Invoice_INV-001.pdf',
      });
    });
  });
});
