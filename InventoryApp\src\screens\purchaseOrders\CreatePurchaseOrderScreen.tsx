import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Appbar,
  Card,
  List,
  IconButton,
  Menu,
  Divider,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { createPurchaseOrder } from '../../store/slices/purchaseOrderSlice';
import { fetchProducts } from '../../store/slices/inventorySlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitCost: number;
  total: number;
}

interface OrderFormData {
  orderNumber: string;
  supplierName: string;
  supplierEmail: string;
  supplierPhone: string;
  orderDate: string;
  expectedDelivery: string;
  items: OrderItem[];
  notes: string;
  shippingCost: number;
  taxRate: number;
}

const CreatePurchaseOrderScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { products } = useSelector((state: RootState) => state.inventory);
  const { isLoading } = useSelector((state: RootState) => state.purchaseOrders);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [productMenuVisible, setProductMenuVisible] = useState(false);

  const [formData, setFormData] = useState<OrderFormData>({
    orderNumber: generateOrderNumber(),
    supplierName: '',
    supplierEmail: '',
    supplierPhone: '',
    orderDate: new Date().toISOString().split('T')[0],
    expectedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    items: [],
    notes: '',
    shippingCost: 0,
    taxRate: 10,
  });

  function generateOrderNumber(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `PO-${year}${month}${day}-${random}`;
  }

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await dispatch(fetchProducts());
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  const addItem = (product: any) => {
    const newItem: OrderItem = {
      id: Date.now().toString(),
      productId: product.id,
      productName: product.name,
      quantity: 1,
      unitCost: product.costPrice || product.unitPrice || 0,
      total: product.costPrice || product.unitPrice || 0,
    };

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
    setProductMenuVisible(false);
  };

  const updateItem = (itemId: string, field: keyof OrderItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === itemId) {
          const updatedItem = { ...item, [field]: value };
          if (field === 'quantity' || field === 'unitCost') {
            updatedItem.total = updatedItem.quantity * updatedItem.unitCost;
          }
          return updatedItem;
        }
        return item;
      }),
    }));
  };

  const removeItem = (itemId: string) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId),
    }));
  };

  const calculateSubtotal = (): number => {
    return formData.items.reduce((sum, item) => sum + item.total, 0);
  };

  const calculateTax = (): number => {
    return (calculateSubtotal() * formData.taxRate) / 100;
  };

  const calculateTotal = (): number => {
    return calculateSubtotal() + calculateTax() + formData.shippingCost;
  };

  const validateForm = (): boolean => {
    if (!formData.supplierName.trim()) {
      Alert.alert(t('validation.error'), t('validation.supplierNameRequired'));
      return false;
    }
    if (formData.items.length === 0) {
      Alert.alert(t('validation.error'), t('validation.orderItemsRequired'));
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      const orderData = {
        ...formData,
        subtotal: calculateSubtotal(),
        taxAmount: calculateTax(),
        totalAmount: calculateTotal(),
        status: 'pending',
        itemsCount: formData.items.length,
      };

      await dispatch(createPurchaseOrder(orderData));
      Alert.alert(t('messages.success'), t('messages.purchaseOrderCreated'), [
        { text: t('common.ok'), onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert(t('messages.error'), t('messages.operationFailed'));
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={t('purchaseOrders.createOrder')} />
        <Appbar.Action icon="check" onPress={handleSave} />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        {/* Order Header */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('purchaseOrders.orderDetails')}
            </Text>

            <TextInput
              label={t('purchaseOrders.orderNumber')}
              value={formData.orderNumber}
              onChangeText={(value) => setFormData(prev => ({ ...prev, orderNumber: value }))}
              style={styles.input}
              mode="outlined"
            />

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('purchaseOrders.orderDate')}
                value={formData.orderDate}
                onChangeText={(value) => setFormData(prev => ({ ...prev, orderDate: value }))}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
              <TextInput
                label={t('purchaseOrders.expectedDelivery')}
                value={formData.expectedDelivery}
                onChangeText={(value) => setFormData(prev => ({ ...prev, expectedDelivery: value }))}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
            </View>
          </Card.Content>
        </Card>

        {/* Supplier Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('purchaseOrders.supplierInfo')}
            </Text>

            <TextInput
              label={t('purchaseOrders.supplierName')}
              value={formData.supplierName}
              onChangeText={(value) => setFormData(prev => ({ ...prev, supplierName: value }))}
              style={styles.input}
              mode="outlined"
            />

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('purchaseOrders.supplierEmail')}
                value={formData.supplierEmail}
                onChangeText={(value) => setFormData(prev => ({ ...prev, supplierEmail: value }))}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="email-address"
              />
              <TextInput
                label={t('purchaseOrders.supplierPhone')}
                value={formData.supplierPhone}
                onChangeText={(value) => setFormData(prev => ({ ...prev, supplierPhone: value }))}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="phone-pad"
              />
            </View>
          </Card.Content>
        </Card>

        {/* Order Items */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={[styles.itemsHeader, isRTL && styles.itemsHeaderRTL]}>
              <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
                {t('purchaseOrders.items')}
              </Text>
              <Menu
                visible={productMenuVisible}
                onDismiss={() => setProductMenuVisible(false)}
                anchor={
                  <IconButton
                    icon="plus"
                    mode="contained"
                    onPress={() => setProductMenuVisible(true)}
                  />
                }
              >
                {products.map((product) => (
                  <Menu.Item
                    key={product.id}
                    onPress={() => addItem(product)}
                    title={`${product.name} - ${formatCurrency(product.costPrice || product.unitPrice || 0)}`}
                  />
                ))}
              </Menu>
            </View>

            {formData.items.map((item) => (
              <Card key={item.id} style={styles.itemCard}>
                <Card.Content>
                  <View style={[styles.itemHeader, isRTL && styles.itemHeaderRTL]}>
                    <Text style={[styles.itemName, isRTL && styles.itemNameRTL]}>
                      {item.productName}
                    </Text>
                    <IconButton
                      icon="delete"
                      size={20}
                      iconColor={colors.error}
                      onPress={() => removeItem(item.id)}
                    />
                  </View>

                  <View style={[styles.itemDetails, isRTL && styles.itemDetailsRTL]}>
                    <TextInput
                      label={t('purchaseOrders.quantity')}
                      value={item.quantity.toString()}
                      onChangeText={(value) => updateItem(item.id, 'quantity', parseInt(value) || 0)}
                      style={styles.itemInput}
                      mode="outlined"
                      keyboardType="numeric"
                    />
                    <TextInput
                      label={t('purchaseOrders.unitCost')}
                      value={item.unitCost.toString()}
                      onChangeText={(value) => updateItem(item.id, 'unitCost', parseFloat(value) || 0)}
                      style={styles.itemInput}
                      mode="outlined"
                      keyboardType="numeric"
                    />
                    <Text style={[styles.itemTotal, isRTL && styles.itemTotalRTL]}>
                      {formatCurrency(item.total)}
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            ))}

            {formData.items.length === 0 && (
              <View style={styles.emptyItems}>
                <Icon name="shopping-cart" size={48} color={colors.onSurfaceVariant} />
                <Text style={[styles.emptyItemsText, isRTL && styles.emptyItemsTextRTL]}>
                  {t('purchaseOrders.noItemsAdded')}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Order Summary */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('purchaseOrders.summary')}
            </Text>

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('purchaseOrders.taxRate')}
                value={formData.taxRate.toString()}
                onChangeText={(value) => setFormData(prev => ({ ...prev, taxRate: parseFloat(value) || 0 }))}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="numeric"
                right={<TextInput.Affix text="%" />}
              />
              <TextInput
                label={t('purchaseOrders.shippingCost')}
                value={formData.shippingCost.toString()}
                onChangeText={(value) => setFormData(prev => ({ ...prev, shippingCost: parseFloat(value) || 0 }))}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="numeric"
              />
            </View>

            <Divider style={styles.divider} />

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                {t('purchaseOrders.subtotal')}:
              </Text>
              <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL]}>
                {formatCurrency(calculateSubtotal())}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                {t('purchaseOrders.tax')}:
              </Text>
              <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL]}>
                {formatCurrency(calculateTax())}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                {t('purchaseOrders.shipping')}:
              </Text>
              <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL]}>
                {formatCurrency(formData.shippingCost)}
              </Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.summaryRow}>
              <Text style={[styles.totalLabel, isRTL && styles.totalLabelRTL]}>
                {t('purchaseOrders.total')}:
              </Text>
              <Text style={[styles.totalValue, isRTL && styles.totalValueRTL]}>
                {formatCurrency(calculateTotal())}
              </Text>
            </View>

            <TextInput
              label={t('purchaseOrders.notes')}
              value={formData.notes}
              onChangeText={(value) => setFormData(prev => ({ ...prev, notes: value }))}
              style={styles.input}
              mode="outlined"
              multiline
              numberOfLines={3}
            />
          </Card.Content>
        </Card>

        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.saveButton}
          loading={isLoading}
          disabled={isLoading}
        >
          {t('purchaseOrders.createOrder')}
        </Button>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  card: {
    marginBottom: spacing.md,
    borderRadius: 12,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
  },
  sectionTitleRTL: {
    textAlign: 'right',
  },
  input: {
    marginBottom: spacing.sm,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rowRTL: {
    flexDirection: 'row-reverse',
  },
  halfInput: {
    width: '48%',
  },
  itemsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  itemsHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  itemCard: {
    marginBottom: spacing.sm,
    backgroundColor: colors.surfaceVariant,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  itemHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurface,
    flex: 1,
  },
  itemNameRTL: {
    textAlign: 'right',
  },
  itemDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemDetailsRTL: {
    flexDirection: 'row-reverse',
  },
  itemInput: {
    width: '30%',
    marginRight: spacing.sm,
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
    width: '25%',
    textAlign: 'right',
  },
  itemTotalRTL: {
    textAlign: 'left',
  },
  emptyItems: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyItemsText: {
    fontSize: 16,
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
  },
  emptyItemsTextRTL: {
    textAlign: 'right',
  },
  divider: {
    marginVertical: spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: 16,
    color: colors.onSurface,
  },
  summaryLabelRTL: {
    textAlign: 'right',
  },
  summaryValue: {
    fontSize: 16,
    color: colors.onSurface,
  },
  summaryValueRTL: {
    textAlign: 'left',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  totalLabelRTL: {
    textAlign: 'right',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  totalValueRTL: {
    textAlign: 'left',
  },
  saveButton: {
    marginVertical: spacing.lg,
    borderRadius: 8,
  },
});

export default CreatePurchaseOrderScreen;
