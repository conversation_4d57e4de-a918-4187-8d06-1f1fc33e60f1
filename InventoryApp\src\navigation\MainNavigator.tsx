import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import Icon from 'react-native-vector-icons/MaterialIcons';

import DashboardScreen from '../screens/dashboard/DashboardScreen';
import InventoryNavigator from './InventoryNavigator';
import CustomerNavigator from './CustomerNavigator';
import InvoiceNavigator from './InvoiceNavigator';
import PurchaseOrderNavigator from './PurchaseOrderNavigator';
import SettingsScreen from '../screens/settings/SettingsScreen';

export type MainTabParamList = {
  Dashboard: undefined;
  Inventory: undefined;
  Customers: undefined;
  Invoices: undefined;
  Settings: undefined;
};

const Tab = createBottomTabNavigator<MainTabParamList>();

const MainNavigator: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useSelector((state: RootState) => state.app);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Inventory':
              iconName = 'inventory';
              break;
            case 'Customers':
              iconName = 'people';
              break;
            case 'Invoices':
              iconName = 'receipt';
              break;
            case 'Settings':
              iconName = 'settings';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#2196F3',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
        tabBarStyle: {
          flexDirection: isRTL ? 'row-reverse' : 'row',
        },
        tabBarLabelStyle: {
          textAlign: isRTL ? 'right' : 'left',
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          tabBarLabel: t('navigation.dashboard'),
        }}
      />
      <Tab.Screen
        name="Inventory"
        component={InventoryNavigator}
        options={{
          tabBarLabel: t('navigation.inventory'),
        }}
      />
      <Tab.Screen
        name="Customers"
        component={CustomerNavigator}
        options={{
          tabBarLabel: t('navigation.customers'),
        }}
      />
      <Tab.Screen
        name="Invoices"
        component={InvoiceNavigator}
        options={{
          tabBarLabel: t('navigation.invoices'),
        }}
      />
      <Tab.Screen
        name="PurchaseOrders"
        component={PurchaseOrderNavigator}
        options={{
          tabBarLabel: t('navigation.purchaseOrders'),
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          tabBarLabel: t('navigation.settings'),
        }}
      />
    </Tab.Navigator>
  );
};

export default MainNavigator;
