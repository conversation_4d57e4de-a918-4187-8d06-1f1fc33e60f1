# Augment Code Complete Removal Script
# This script removes Augment Code from your system and all projects
# Run as Administrator for complete removal

param(
    [switch]$Force,
    [switch]$Verbose,
    [switch]$WhatIf
)

# Set execution policy and error handling
Set-StrictMode -Version Latest
$ErrorActionPreference = "Continue"

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Section {
    param([string]$Title)
    Write-Host "`n" + "="*60 -ForegroundColor Blue
    Write-Host $Title -ForegroundColor Blue
    Write-Host "="*60 -ForegroundColor Blue
}

function Remove-ItemSafely {
    param(
        [string]$Path,
        [string]$Description,
        [switch]$Recurse
    )
    
    if (Test-Path $Path) {
        Write-ColorOutput "Found: $Description at $Path" $Yellow
        if ($WhatIf) {
            Write-ColorOutput "WHAT-IF: Would remove $Path" $Blue
        } else {
            try {
                if ($Recurse) {
                    Remove-Item -Path $Path -Recurse -Force -ErrorAction Stop
                } else {
                    Remove-Item -Path $Path -Force -ErrorAction Stop
                }
                Write-ColorOutput "✓ Removed: $Description" $Green
            } catch {
                Write-ColorOutput "✗ Failed to remove $Description`: $($_.Exception.Message)" $Red
            }
        }
    } else {
        if ($Verbose) {
            Write-ColorOutput "Not found: $Description at $Path" "Gray"
        }
    }
}

function Stop-AugmentProcesses {
    Write-Section "Stopping Augment Processes"
    
    $processNames = @(
        "augment*",
        "Augment*",
        "*augment*"
    )
    
    foreach ($processName in $processNames) {
        $processes = Get-Process -Name $processName -ErrorAction SilentlyContinue
        foreach ($process in $processes) {
            Write-ColorOutput "Stopping process: $($process.Name) (PID: $($process.Id))" $Yellow
            if (-not $WhatIf) {
                try {
                    $process.Kill()
                    Write-ColorOutput "✓ Stopped process: $($process.Name)" $Green
                } catch {
                    Write-ColorOutput "✗ Failed to stop process: $($process.Name)" $Red
                }
            }
        }
    }
}

function Remove-AugmentFromSystem {
    Write-Section "Removing Augment from System Directories"
    
    # Common installation directories
    $systemPaths = @(
        "$env:ProgramFiles\Augment",
        "$env:ProgramFiles\Augment Code",
        "$env:ProgramFiles\AugmentCode",
        "${env:ProgramFiles(x86)}\Augment",
        "${env:ProgramFiles(x86)}\Augment Code",
        "${env:ProgramFiles(x86)}\AugmentCode",
        "$env:LOCALAPPDATA\Augment",
        "$env:LOCALAPPDATA\AugmentCode",
        "$env:LOCALAPPDATA\Augment Code",
        "$env:APPDATA\Augment",
        "$env:APPDATA\AugmentCode",
        "$env:APPDATA\Augment Code"
    )
    
    foreach ($path in $systemPaths) {
        Remove-ItemSafely -Path $path -Description "Augment installation directory" -Recurse
    }
}

function Remove-AugmentFromUserProfile {
    Write-Section "Removing Augment from User Profile"
    
    # User profile directories
    $userPaths = @(
        "$env:USERPROFILE\.augment",
        "$env:USERPROFILE\.augmentcode",
        "$env:USERPROFILE\AppData\Local\Augment",
        "$env:USERPROFILE\AppData\Local\AugmentCode",
        "$env:USERPROFILE\AppData\Roaming\Augment",
        "$env:USERPROFILE\AppData\Roaming\AugmentCode",
        "$env:USERPROFILE\Documents\Augment",
        "$env:USERPROFILE\Documents\AugmentCode"
    )
    
    foreach ($path in $userPaths) {
        Remove-ItemSafely -Path $path -Description "Augment user data" -Recurse
    }
}

function Remove-AugmentFromVSCode {
    Write-Section "Removing Augment Extensions from VS Code"
    
    # VS Code extensions
    $vscodeExtensionPaths = @(
        "$env:USERPROFILE\.vscode\extensions\*augment*",
        "$env:USERPROFILE\.vscode\extensions\*Augment*"
    )
    
    foreach ($pattern in $vscodeExtensionPaths) {
        $extensions = Get-ChildItem -Path $pattern -ErrorAction SilentlyContinue
        foreach ($extension in $extensions) {
            Remove-ItemSafely -Path $extension.FullName -Description "VS Code Augment extension" -Recurse
        }
    }
    
    # Try to uninstall via code command
    if (Get-Command code -ErrorAction SilentlyContinue) {
        Write-ColorOutput "Attempting to uninstall Augment extensions via VS Code CLI..." $Yellow
        if (-not $WhatIf) {
            try {
                & code --list-extensions | Where-Object { $_ -like "*augment*" -or $_ -like "*Augment*" } | ForEach-Object {
                    Write-ColorOutput "Uninstalling extension: $_" $Yellow
                    & code --uninstall-extension $_
                }
            } catch {
                Write-ColorOutput "Could not uninstall via VS Code CLI: $($_.Exception.Message)" $Red
            }
        }
    }
}

function Remove-AugmentFromProjects {
    Write-Section "Removing Augment from Projects"
    
    # Search for Augment files in common project directories
    $projectDirs = @(
        "$env:USERPROFILE\Documents",
        "$env:USERPROFILE\Desktop",
        "$env:USERPROFILE\source",
        "$env:USERPROFILE\repos",
        "$env:USERPROFILE\projects",
        "C:\projects",
        "C:\source",
        "C:\repos"
    )
    
    $augmentFiles = @(
        ".augment",
        ".augmentcode",
        "augment.json",
        "augment.config.js",
        "augment.config.json",
        ".augment.json",
        "node_modules\*augment*",
        "package-lock.json" # Will be regenerated without augment dependencies
    )
    
    foreach ($dir in $projectDirs) {
        if (Test-Path $dir) {
            Write-ColorOutput "Scanning directory: $dir" $Blue
            
            foreach ($filePattern in $augmentFiles) {
                $files = Get-ChildItem -Path "$dir\*" -Include $filePattern -Recurse -Force -ErrorAction SilentlyContinue
                foreach ($file in $files) {
                    Remove-ItemSafely -Path $file.FullName -Description "Project Augment file" -Recurse
                }
            }
        }
    }
}

function Remove-AugmentFromRegistry {
    Write-Section "Removing Augment from Windows Registry"
    
    $registryPaths = @(
        "HKCU:\Software\Augment",
        "HKCU:\Software\AugmentCode",
        "HKLM:\Software\Augment",
        "HKLM:\Software\AugmentCode",
        "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*Augment*",
        "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*Augment*"
    )
    
    foreach ($regPath in $registryPaths) {
        if ($regPath -like "*\*Augment*") {
            # Handle wildcard paths
            $basePath = $regPath -replace '\\\*Augment\*$', ''
            if (Test-Path $basePath) {
                $keys = Get-ChildItem -Path $basePath -ErrorAction SilentlyContinue | Where-Object { $_.Name -like "*Augment*" }
                foreach ($key in $keys) {
                    Write-ColorOutput "Found registry key: $($key.Name)" $Yellow
                    if (-not $WhatIf) {
                        try {
                            Remove-Item -Path $key.PSPath -Recurse -Force
                            Write-ColorOutput "✓ Removed registry key: $($key.Name)" $Green
                        } catch {
                            Write-ColorOutput "✗ Failed to remove registry key: $($_.Exception.Message)" $Red
                        }
                    }
                }
            }
        } else {
            if (Test-Path $regPath) {
                Write-ColorOutput "Found registry path: $regPath" $Yellow
                if (-not $WhatIf) {
                    try {
                        Remove-Item -Path $regPath -Recurse -Force
                        Write-ColorOutput "✓ Removed registry path: $regPath" $Green
                    } catch {
                        Write-ColorOutput "✗ Failed to remove registry path: $($_.Exception.Message)" $Red
                    }
                }
            }
        }
    }
}

function Remove-AugmentFromEnvironment {
    Write-Section "Removing Augment from Environment Variables"
    
    # Check and remove environment variables
    $envVars = @("AUGMENT_HOME", "AUGMENT_PATH", "AUGMENT_CONFIG")
    
    foreach ($var in $envVars) {
        $value = [Environment]::GetEnvironmentVariable($var, "User")
        if ($value) {
            Write-ColorOutput "Found environment variable: $var = $value" $Yellow
            if (-not $WhatIf) {
                [Environment]::SetEnvironmentVariable($var, $null, "User")
                Write-ColorOutput "✓ Removed environment variable: $var" $Green
            }
        }
        
        $value = [Environment]::GetEnvironmentVariable($var, "Machine")
        if ($value) {
            Write-ColorOutput "Found system environment variable: $var = $value" $Yellow
            if (-not $WhatIf) {
                try {
                    [Environment]::SetEnvironmentVariable($var, $null, "Machine")
                    Write-ColorOutput "✓ Removed system environment variable: $var" $Green
                } catch {
                    Write-ColorOutput "✗ Failed to remove system environment variable (run as admin): $var" $Red
                }
            }
        }
    }
    
    # Remove from PATH
    $userPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($userPath -and ($userPath -like "*augment*" -or $userPath -like "*Augment*")) {
        Write-ColorOutput "Found Augment in user PATH" $Yellow
        if (-not $WhatIf) {
            $newPath = ($userPath -split ';' | Where-Object { $_ -notlike "*augment*" -and $_ -notlike "*Augment*" }) -join ';'
            [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
            Write-ColorOutput "✓ Removed Augment from user PATH" $Green
        }
    }
}

function Clean-PackageManagers {
    Write-Section "Cleaning Package Managers"
    
    # NPM global packages
    if (Get-Command npm -ErrorAction SilentlyContinue) {
        Write-ColorOutput "Checking NPM global packages..." $Blue
        if (-not $WhatIf) {
            try {
                $globalPackages = & npm list -g --depth=0 --json | ConvertFrom-Json
                if ($globalPackages.dependencies) {
                    $globalPackages.dependencies.PSObject.Properties | Where-Object { $_.Name -like "*augment*" } | ForEach-Object {
                        Write-ColorOutput "Uninstalling NPM package: $($_.Name)" $Yellow
                        & npm uninstall -g $_.Name
                    }
                }
            } catch {
                Write-ColorOutput "Could not check NPM packages: $($_.Exception.Message)" $Red
            }
        }
    }
}

# Main execution
Write-ColorOutput "Augment Code Complete Removal Script" $Blue
Write-ColorOutput "====================================" $Blue

if ($WhatIf) {
    Write-ColorOutput "`nRunning in WHAT-IF mode - no changes will be made" $Yellow
}

if (-not $Force) {
    $confirmation = Read-Host "`nThis will completely remove Augment Code from your system and all projects. Continue? (y/N)"
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-ColorOutput "Operation cancelled by user." $Yellow
        exit 0
    }
}

# Execute removal steps
Stop-AugmentProcesses
Remove-AugmentFromSystem
Remove-AugmentFromUserProfile
Remove-AugmentFromVSCode
Remove-AugmentFromProjects
Remove-AugmentFromRegistry
Remove-AugmentFromEnvironment
Clean-PackageManagers

Write-Section "Removal Complete"
Write-ColorOutput "Augment Code removal process completed!" $Green
Write-ColorOutput "You may need to restart your computer for all changes to take effect." $Yellow
Write-ColorOutput "If you encounter any issues, run this script as Administrator." $Yellow
