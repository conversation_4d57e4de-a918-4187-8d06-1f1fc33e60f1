import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import inventorySlice from './slices/inventorySlice';
import customerSlice from './slices/customerSlice';
import invoiceSlice from './slices/invoiceSlice';
import purchaseOrderSlice from './slices/purchaseOrderSlice';
import financialSlice from './slices/financialSlice';
import appSlice from './slices/appSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    inventory: inventorySlice,
    customers: customerSlice,
    invoices: invoiceSlice,
    purchaseOrders: purchaseOrderSlice,
    financial: financialSlice,
    app: appSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
