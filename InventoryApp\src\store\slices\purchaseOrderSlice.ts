import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { DatabaseService } from '../../database/DatabaseService';

export interface PurchaseOrderItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitCost: number;
  total: number;
}

export interface PurchaseOrder {
  id: string;
  orderNumber: string;
  supplierName: string;
  supplierEmail?: string;
  supplierPhone?: string;
  orderDate: string;
  expectedDelivery?: string;
  status: 'pending' | 'approved' | 'ordered' | 'received' | 'cancelled';
  items: PurchaseOrderItem[];
  subtotal: number;
  taxAmount: number;
  shippingCost: number;
  totalAmount: number;
  itemsCount: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface PurchaseOrderState {
  purchaseOrders: PurchaseOrder[];
  isLoading: boolean;
  error: string | null;
}

const initialState: PurchaseOrderState = {
  purchaseOrders: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchPurchaseOrders = createAsyncThunk(
  'purchaseOrders/fetchAll',
  async () => {
    const db = DatabaseService.getInstance();
    return await db.getAllPurchaseOrders();
  }
);

export const fetchPurchaseOrderById = createAsyncThunk(
  'purchaseOrders/fetchById',
  async (id: string) => {
    const db = DatabaseService.getInstance();
    return await db.getPurchaseOrderById(id);
  }
);

export const createPurchaseOrder = createAsyncThunk(
  'purchaseOrders/create',
  async (orderData: Omit<PurchaseOrder, 'id' | 'createdAt' | 'updatedAt'>) => {
    const db = DatabaseService.getInstance();
    const id = await db.createPurchaseOrder(orderData);
    return { ...orderData, id };
  }
);

export const updatePurchaseOrder = createAsyncThunk(
  'purchaseOrders/update',
  async ({ id, ...updateData }: Partial<PurchaseOrder> & { id: string }) => {
    const db = DatabaseService.getInstance();
    await db.updatePurchaseOrder(id, updateData);
    return { id, ...updateData };
  }
);

export const updatePurchaseOrderStatus = createAsyncThunk(
  'purchaseOrders/updateStatus',
  async ({ id, status }: { id: string; status: PurchaseOrder['status'] }) => {
    const db = DatabaseService.getInstance();
    await db.updatePurchaseOrderStatus(id, status);
    return { id, status };
  }
);

export const deletePurchaseOrder = createAsyncThunk(
  'purchaseOrders/delete',
  async (id: string) => {
    const db = DatabaseService.getInstance();
    await db.deletePurchaseOrder(id);
    return id;
  }
);

export const receivePurchaseOrder = createAsyncThunk(
  'purchaseOrders/receive',
  async ({ id, receivedItems }: { id: string; receivedItems: { productId: string; quantity: number }[] }) => {
    const db = DatabaseService.getInstance();
    await db.receivePurchaseOrder(id, receivedItems);
    return { id, status: 'received' as const };
  }
);

const purchaseOrderSlice = createSlice({
  name: 'purchaseOrders',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setPurchaseOrders: (state, action: PayloadAction<PurchaseOrder[]>) => {
      state.purchaseOrders = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch all purchase orders
      .addCase(fetchPurchaseOrders.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPurchaseOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.purchaseOrders = action.payload;
      })
      .addCase(fetchPurchaseOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch purchase orders';
      })

      // Fetch purchase order by ID
      .addCase(fetchPurchaseOrderById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPurchaseOrderById.fulfilled, (state, action) => {
        state.isLoading = false;
        const existingIndex = state.purchaseOrders.findIndex(order => order.id === action.payload.id);
        if (existingIndex >= 0) {
          state.purchaseOrders[existingIndex] = action.payload;
        } else {
          state.purchaseOrders.push(action.payload);
        }
      })
      .addCase(fetchPurchaseOrderById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch purchase order';
      })

      // Create purchase order
      .addCase(createPurchaseOrder.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPurchaseOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        const newOrder: PurchaseOrder = {
          ...action.payload,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        state.purchaseOrders.unshift(newOrder);
      })
      .addCase(createPurchaseOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to create purchase order';
      })

      // Update purchase order
      .addCase(updatePurchaseOrder.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePurchaseOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.purchaseOrders.findIndex(order => order.id === action.payload.id);
        if (index >= 0) {
          state.purchaseOrders[index] = {
            ...state.purchaseOrders[index],
            ...action.payload,
            updatedAt: new Date().toISOString(),
          };
        }
      })
      .addCase(updatePurchaseOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update purchase order';
      })

      // Update purchase order status
      .addCase(updatePurchaseOrderStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePurchaseOrderStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.purchaseOrders.findIndex(order => order.id === action.payload.id);
        if (index >= 0) {
          state.purchaseOrders[index].status = action.payload.status;
          state.purchaseOrders[index].updatedAt = new Date().toISOString();
        }
      })
      .addCase(updatePurchaseOrderStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update purchase order status';
      })

      // Delete purchase order
      .addCase(deletePurchaseOrder.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePurchaseOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.purchaseOrders = state.purchaseOrders.filter(order => order.id !== action.payload);
      })
      .addCase(deletePurchaseOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to delete purchase order';
      })

      // Receive purchase order
      .addCase(receivePurchaseOrder.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(receivePurchaseOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.purchaseOrders.findIndex(order => order.id === action.payload.id);
        if (index >= 0) {
          state.purchaseOrders[index].status = action.payload.status;
          state.purchaseOrders[index].updatedAt = new Date().toISOString();
        }
      })
      .addCase(receivePurchaseOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to receive purchase order';
      });
  },
});

export const { clearError, setPurchaseOrders } = purchaseOrderSlice.actions;
export default purchaseOrderSlice.reducer;
