import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// Import screens
import PurchaseOrdersScreen from '../screens/purchaseOrders/PurchaseOrdersScreen';
import CreatePurchaseOrderScreen from '../screens/purchaseOrders/CreatePurchaseOrderScreen';

export type PurchaseOrderStackParamList = {
  PurchaseOrdersList: undefined;
  CreatePurchaseOrder: { supplierId?: string };
  EditPurchaseOrder: { orderId: string };
  PurchaseOrderDetails: { orderId: string };
};

const Stack = createStackNavigator<PurchaseOrderStackParamList>();

const PurchaseOrderNavigator: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Stack.Navigator
      initialRouteName="PurchaseOrdersList"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen 
        name="PurchaseOrdersList" 
        component={PurchaseOrdersScreen}
        options={{
          title: t('navigation.purchaseOrders'),
        }}
      />
      <Stack.Screen 
        name="CreatePurchaseOrder" 
        component={CreatePurchaseOrderScreen}
        options={{
          title: t('purchaseOrders.createOrder'),
        }}
      />
    </Stack.Navigator>
  );
};

export default PurchaseOrderNavigator;
