import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { Provider } from 'react-redux';
import { PaperProvider } from 'react-native-paper';
import { I18nextProvider } from 'react-i18next';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { store } from './src/store';
import i18n from './src/i18n';
import DatabaseService from './src/database/DatabaseService';
import AppNavigator from './src/navigation/AppNavigator';
import { theme } from './src/theme';
import LoadingScreen from './src/components/LoadingScreen';

export default function App() {
  const [isInitialized, setIsInitialized] = React.useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize database
        await DatabaseService.initialize();

        // Add any other initialization logic here
        setIsInitialized(true);
      } catch (error) {
        console.error('App initialization failed:', error);
        // Handle initialization error
        setIsInitialized(true); // Still allow app to load
      }
    };

    initializeApp();
  }, []);

  if (!isInitialized) {
    return <LoadingScreen />;
  }

  return (
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        <PaperProvider theme={theme}>
          <SafeAreaProvider>
            <NavigationContainer>
              <AppNavigator />
              <StatusBar style="auto" />
            </NavigationContainer>
          </SafeAreaProvider>
        </PaperProvider>
      </I18nextProvider>
    </Provider>
  );
}
