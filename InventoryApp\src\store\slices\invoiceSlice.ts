import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Invoice, InvoiceItem } from '../../types';
import DatabaseService from '../../database/DatabaseService';

interface InvoiceState {
  invoices: Invoice[];
  selectedInvoice: Invoice | null;
  invoiceItems: InvoiceItem[];
  isLoading: boolean;
  error: string | null;
}

const initialState: InvoiceState = {
  invoices: [],
  selectedInvoice: null,
  invoiceItems: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchInvoices = createAsyncThunk(
  'invoices/fetchInvoices',
  async (_, { rejectWithValue }) => {
    try {
      const invoices = await DatabaseService.getInvoices();
      return invoices;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch invoices');
    }
  }
);

export const createInvoice = createAsyncThunk(
  'invoices/createInvoice',
  async (invoiceData: any, { rejectWithValue }) => {
    try {
      await DatabaseService.createInvoice(invoiceData);
      const invoices = await DatabaseService.getInvoices();
      return invoices;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create invoice');
    }
  }
);

export const updateInvoice = createAsyncThunk(
  'invoices/updateInvoice',
  async ({ id, invoiceData }: { id: string; invoiceData: any }, { rejectWithValue }) => {
    try {
      // Note: You'll need to implement updateInvoice in DatabaseService
      // await DatabaseService.updateInvoice(id, invoiceData);
      const invoices = await DatabaseService.getInvoices();
      return invoices;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update invoice');
    }
  }
);

export const deleteInvoice = createAsyncThunk(
  'invoices/deleteInvoice',
  async (invoiceId: string, { rejectWithValue }) => {
    try {
      // Note: You'll need to implement deleteInvoice in DatabaseService
      // await DatabaseService.deleteInvoice(invoiceId);
      const invoices = await DatabaseService.getInvoices();
      return invoices;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete invoice');
    }
  }
);

export const fetchInvoiceItems = createAsyncThunk(
  'invoices/fetchInvoiceItems',
  async (invoiceId: string, { rejectWithValue }) => {
    try {
      // Note: You'll need to implement getInvoiceItems in DatabaseService
      // const items = await DatabaseService.getInvoiceItems(invoiceId);
      // return items;
      return [];
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch invoice items');
    }
  }
);

const invoiceSlice = createSlice({
  name: 'invoices',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedInvoice: (state, action: PayloadAction<Invoice | null>) => {
      state.selectedInvoice = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    addInvoiceItem: (state, action: PayloadAction<InvoiceItem>) => {
      state.invoiceItems.push(action.payload);
    },
    updateInvoiceItem: (state, action: PayloadAction<{ index: number; item: InvoiceItem }>) => {
      const { index, item } = action.payload;
      if (state.invoiceItems[index]) {
        state.invoiceItems[index] = item;
      }
    },
    removeInvoiceItem: (state, action: PayloadAction<number>) => {
      state.invoiceItems.splice(action.payload, 1);
    },
    clearInvoiceItems: (state) => {
      state.invoiceItems = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch invoices
      .addCase(fetchInvoices.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchInvoices.fulfilled, (state, action) => {
        state.isLoading = false;
        state.invoices = action.payload;
      })
      .addCase(fetchInvoices.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Create invoice
      .addCase(createInvoice.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createInvoice.fulfilled, (state, action) => {
        state.isLoading = false;
        state.invoices = action.payload;
      })
      .addCase(createInvoice.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update invoice
      .addCase(updateInvoice.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateInvoice.fulfilled, (state, action) => {
        state.isLoading = false;
        state.invoices = action.payload;
      })
      .addCase(updateInvoice.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Delete invoice
      .addCase(deleteInvoice.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteInvoice.fulfilled, (state, action) => {
        state.isLoading = false;
        state.invoices = action.payload;
      })
      .addCase(deleteInvoice.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch invoice items
      .addCase(fetchInvoiceItems.fulfilled, (state, action) => {
        state.invoiceItems = action.payload;
      });
  },
});

export const {
  clearError,
  setSelectedInvoice,
  setLoading,
  addInvoiceItem,
  updateInvoiceItem,
  removeInvoiceItem,
  clearInvoiceItems,
} = invoiceSlice.actions;

export default invoiceSlice.reducer;
