import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

// Custom color palette
const colors = {
  primary: '#2196F3',
  primaryContainer: '#E3F2FD',
  secondary: '#FF9800',
  secondaryContainer: '#FFF3E0',
  tertiary: '#4CAF50',
  tertiaryContainer: '#E8F5E8',
  surface: '#FFFFFF',
  surfaceVariant: '#F5F5F5',
  background: '#FAFAFA',
  error: '#F44336',
  errorContainer: '#FFEBEE',
  onPrimary: '#FFFFFF',
  onPrimaryContainer: '#1976D2',
  onSecondary: '#FFFFFF',
  onSecondaryContainer: '#E65100',
  onTertiary: '#FFFFFF',
  onTertiaryContainer: '#2E7D32',
  onSurface: '#212121',
  onSurfaceVariant: '#757575',
  onBackground: '#212121',
  onError: '#FFFFFF',
  onErrorContainer: '#C62828',
  outline: '#BDBDBD',
  outlineVariant: '#E0E0E0',
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#303030',
  inverseOnSurface: '#F5F5F5',
  inversePrimary: '#90CAF9',
  elevation: {
    level0: 'transparent',
    level1: '#F8F9FA',
    level2: '#F1F3F4',
    level3: '#E8EAED',
    level4: '#E0E3E6',
    level5: '#D9DCDF',
  },
};

const darkColors = {
  primary: '#90CAF9',
  primaryContainer: '#1565C0',
  secondary: '#FFB74D',
  secondaryContainer: '#F57C00',
  tertiary: '#81C784',
  tertiaryContainer: '#388E3C',
  surface: '#121212',
  surfaceVariant: '#1E1E1E',
  background: '#000000',
  error: '#CF6679',
  errorContainer: '#B00020',
  onPrimary: '#000000',
  onPrimaryContainer: '#E3F2FD',
  onSecondary: '#000000',
  onSecondaryContainer: '#FFF3E0',
  onTertiary: '#000000',
  onTertiaryContainer: '#E8F5E8',
  onSurface: '#FFFFFF',
  onSurfaceVariant: '#BDBDBD',
  onBackground: '#FFFFFF',
  onError: '#000000',
  onErrorContainer: '#FFEBEE',
  outline: '#757575',
  outlineVariant: '#424242',
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#F5F5F5',
  inverseOnSurface: '#303030',
  inversePrimary: '#2196F3',
  elevation: {
    level0: 'transparent',
    level1: '#1E1E1E',
    level2: '#232323',
    level3: '#252525',
    level4: '#272727',
    level5: '#2C2C2C',
  },
};

export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...colors,
  },
  roundness: 8,
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    ...darkColors,
  },
  roundness: 8,
};

export const theme = lightTheme;

// Common styles
export const commonStyles = {
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 12,
    elevation: 2,
  },
  row: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
  },
  spaceBetween: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  center: {
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  shadow: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  textInput: {
    marginVertical: 8,
  },
  button: {
    marginVertical: 8,
    borderRadius: 8,
  },
  fab: {
    position: 'absolute' as const,
    margin: 16,
    right: 0,
    bottom: 0,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold' as const,
    color: colors.onSurface,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: colors.onSurface,
    marginVertical: 12,
  },
  bodyText: {
    fontSize: 16,
    color: colors.onSurface,
  },
  captionText: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
  },
  successText: {
    fontSize: 14,
    color: colors.tertiary,
  },
};

// RTL support
export const rtlStyles = {
  textAlign: 'right' as const,
  writingDirection: 'rtl' as const,
};

// Spacing
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Typography
export const typography = {
  h1: {
    fontSize: 32,
    fontWeight: 'bold' as const,
    lineHeight: 40,
  },
  h2: {
    fontSize: 28,
    fontWeight: 'bold' as const,
    lineHeight: 36,
  },
  h3: {
    fontSize: 24,
    fontWeight: '600' as const,
    lineHeight: 32,
  },
  h4: {
    fontSize: 20,
    fontWeight: '600' as const,
    lineHeight: 28,
  },
  h5: {
    fontSize: 18,
    fontWeight: '500' as const,
    lineHeight: 24,
  },
  h6: {
    fontSize: 16,
    fontWeight: '500' as const,
    lineHeight: 22,
  },
  body1: {
    fontSize: 16,
    fontWeight: 'normal' as const,
    lineHeight: 24,
  },
  body2: {
    fontSize: 14,
    fontWeight: 'normal' as const,
    lineHeight: 20,
  },
  caption: {
    fontSize: 12,
    fontWeight: 'normal' as const,
    lineHeight: 16,
  },
  button: {
    fontSize: 14,
    fontWeight: '500' as const,
    lineHeight: 20,
    textTransform: 'uppercase' as const,
  },
};

// Layout
export const layout = {
  window: {
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
  },
  row: {
    flexDirection: 'row' as const,
  },
  column: {
    flexDirection: 'column' as const,
  },
  center: {
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  spaceBetween: {
    justifyContent: 'space-between' as const,
  },
  spaceAround: {
    justifyContent: 'space-around' as const,
  },
  spaceEvenly: {
    justifyContent: 'space-evenly' as const,
  },
  alignStart: {
    alignItems: 'flex-start' as const,
  },
  alignEnd: {
    alignItems: 'flex-end' as const,
  },
  alignCenter: {
    alignItems: 'center' as const,
  },
  alignStretch: {
    alignItems: 'stretch' as const,
  },
};
