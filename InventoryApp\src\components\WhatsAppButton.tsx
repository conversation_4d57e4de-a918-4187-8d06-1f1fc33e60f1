import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Button, Menu, IconButton } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { colors, spacing } from '../theme';
import WhatsAppService from '../services/WhatsAppService';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface WhatsAppButtonProps {
  contact: {
    name: string;
    phone: string;
    email?: string;
  };
  data?: any;
  type?: 'message' | 'invoice' | 'purchaseOrder' | 'customerInfo' | 'stockAlert';
  documentUri?: string;
  style?: any;
  mode?: 'contained' | 'outlined' | 'text';
  compact?: boolean;
  iconOnly?: boolean;
}

const WhatsAppButton: React.FC<WhatsAppButtonProps> = ({
  contact,
  data,
  type = 'message',
  documentUri,
  style,
  mode = 'outlined',
  compact = false,
  iconOnly = false,
}) => {
  const { t } = useTranslation();
  const { isRTL } = useSelector((state: RootState) => state.app);
  const [isLoading, setIsLoading] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);

  const whatsAppService = WhatsAppService.getInstance();

  const handleSend = async (sendType?: string) => {
    if (!contact.phone) {
      Alert.alert(t('messages.error'), t('messages.phoneNumberRequired'));
      return;
    }

    setIsLoading(true);
    setMenuVisible(false);

    try {
      let success = false;
      const actualType = sendType || type;

      switch (actualType) {
        case 'message':
          const message = data?.message || t('whatsapp.defaultMessage');
          success = await whatsAppService.sendMessage(contact.phone, message);
          break;

        case 'invoice':
          if (!data) {
            Alert.alert(t('messages.error'), t('messages.invoiceDataRequired'));
            return;
          }
          success = await whatsAppService.sendInvoice(contact, data, documentUri);
          break;

        case 'purchaseOrder':
          if (!data) {
            Alert.alert(t('messages.error'), t('messages.purchaseOrderDataRequired'));
            return;
          }
          success = await whatsAppService.sendPurchaseOrder(contact, data, documentUri);
          break;

        case 'customerInfo':
          if (!data) {
            Alert.alert(t('messages.error'), t('messages.customerDataRequired'));
            return;
          }
          success = await whatsAppService.sendCustomerInfo(contact, data);
          break;

        case 'stockAlert':
          if (!data || !Array.isArray(data)) {
            Alert.alert(t('messages.error'), t('messages.stockDataRequired'));
            return;
          }
          success = await whatsAppService.sendStockAlert(contact, data);
          break;

        default:
          Alert.alert(t('messages.error'), t('messages.unsupportedMessageType'));
          return;
      }

      if (success) {
        // Optional: Show success message
        // Alert.alert(t('messages.success'), t('messages.whatsappMessageSent'));
      }
    } catch (error) {
      console.error('WhatsApp send error:', error);
      Alert.alert(t('messages.error'), t('messages.whatsappSendFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const getButtonText = () => {
    switch (type) {
      case 'invoice':
        return t('whatsapp.sendInvoice');
      case 'purchaseOrder':
        return t('whatsapp.sendPurchaseOrder');
      case 'customerInfo':
        return t('whatsapp.sendCustomerInfo');
      case 'stockAlert':
        return t('whatsapp.sendStockAlert');
      default:
        return t('whatsapp.sendMessage');
    }
  };

  const getButtonIcon = () => {
    return 'whatsapp';
  };

  const menuOptions = [
    { key: 'message', label: t('whatsapp.sendMessage'), icon: 'message' },
    { key: 'invoice', label: t('whatsapp.sendInvoice'), icon: 'receipt' },
    { key: 'purchaseOrder', label: t('whatsapp.sendPurchaseOrder'), icon: 'shopping-cart' },
    { key: 'customerInfo', label: t('whatsapp.sendCustomerInfo'), icon: 'person' },
    { key: 'stockAlert', label: t('whatsapp.sendStockAlert'), icon: 'warning' },
  ];

  if (iconOnly) {
    return (
      <View style={[styles.container, style]}>
        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={
            <IconButton
              icon="whatsapp"
              iconColor={colors.tertiary}
              size={24}
              onPress={() => setMenuVisible(true)}
              disabled={isLoading}
            />
          }
        >
          {menuOptions.map((option) => (
            <Menu.Item
              key={option.key}
              onPress={() => handleSend(option.key)}
              title={option.label}
              leadingIcon={option.icon}
            />
          ))}
        </Menu>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <Button
        mode={mode}
        onPress={() => handleSend()}
        loading={isLoading}
        disabled={isLoading}
        icon={getButtonIcon()}
        compact={compact}
        style={[
          styles.button,
          mode === 'contained' && styles.containedButton,
          isRTL && styles.buttonRTL
        ]}
        labelStyle={[
          styles.buttonLabel,
          mode === 'contained' && styles.containedButtonLabel
        ]}
      >
        {getButtonText()}
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  button: {
    borderColor: colors.tertiary,
    borderRadius: 8,
  },
  buttonRTL: {
    flexDirection: 'row-reverse',
  },
  containedButton: {
    backgroundColor: colors.tertiary,
  },
  buttonLabel: {
    color: colors.tertiary,
    fontSize: 14,
    fontWeight: '600',
  },
  containedButtonLabel: {
    color: colors.onTertiary,
  },
});

export default WhatsAppButton;
