import DatabaseService from '../../src/database/DatabaseService';

// Mock SQLite
jest.mock('expo-sqlite', () => ({
  openDatabase: jest.fn(() => ({
    transaction: jest.fn((callback) => {
      const tx = {
        executeSql: jest.fn((sql, params, successCallback, errorCallback) => {
          // Mock successful execution
          if (successCallback) {
            successCallback(tx, { rows: { _array: [], length: 0 } });
          }
        }),
      };
      callback(tx);
    }),
  })),
}));

describe('DatabaseService', () => {
  let databaseService: DatabaseService;

  beforeEach(() => {
    databaseService = DatabaseService.getInstance();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Product Management', () => {
    test('should create a new product', async () => {
      const productData = {
        name: 'Test Product',
        name_ar: 'منتج تجريبي',
        sku: 'TEST001',
        barcode: '1234567890',
        category: 'Electronics',
        unitPrice: 100,
        costPrice: 80,
        stockLevel: 50,
        minStock: 10,
        maxStock: 100,
        warehouse: 'Main Warehouse',
        description: 'Test product description',
        description_ar: 'وصف المنتج التجريبي',
        unit: 'piece',
        weight: 1.5,
        dimensions: '10x10x10',
        color: 'Blue',
        size: 'Medium',
        brand: 'Test Brand',
        model: 'TM001',
        serialNumber: 'SN001',
        warranty: '1 year',
        supplier: 'Test Supplier',
        location: 'A1-B2-C3',
        isActive: true,
      };

      const result = await databaseService.createProduct(productData);
      expect(result).toBeDefined();
      expect(typeof result).toBe('string'); // Should return product ID
    });

    test('should get all products', async () => {
      const products = await databaseService.getAllProducts();
      expect(Array.isArray(products)).toBe(true);
    });

    test('should update product stock', async () => {
      const productId = 'test-product-id';
      const newStock = 75;
      
      await expect(
        databaseService.updateProductStock(productId, newStock)
      ).resolves.not.toThrow();
    });

    test('should get low stock products', async () => {
      const lowStockProducts = await databaseService.getLowStockProducts();
      expect(Array.isArray(lowStockProducts)).toBe(true);
    });
  });

  describe('Customer Management', () => {
    test('should create a new customer', async () => {
      const customerData = {
        name: 'Test Customer',
        name_ar: 'عميل تجريبي',
        email: '<EMAIL>',
        phone: '+1234567890',
        type: 'individual',
        address: '123 Test Street',
        address_ar: '123 شارع التجربة',
        city: 'Test City',
        country: 'Test Country',
        zipCode: '12345',
        taxNumber: 'TAX123',
        creditLimit: 5000,
        paymentTerms: 'Net 30',
        notes: 'Test customer notes',
        notes_ar: 'ملاحظات العميل التجريبي',
        isActive: true,
      };

      const result = await databaseService.createCustomer(customerData);
      expect(result).toBeDefined();
      expect(typeof result).toBe('string'); // Should return customer ID
    });

    test('should get all customers', async () => {
      const customers = await databaseService.getAllCustomers();
      expect(Array.isArray(customers)).toBe(true);
    });

    test('should search customers by name', async () => {
      const searchTerm = 'Test';
      const customers = await databaseService.searchCustomers(searchTerm);
      expect(Array.isArray(customers)).toBe(true);
    });
  });

  describe('Invoice Management', () => {
    test('should create a new invoice', async () => {
      const invoiceData = {
        customerId: 'test-customer-id',
        customerName: 'Test Customer',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        customerAddress: '123 Test Street',
        date: new Date().toISOString(),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        items: [
          {
            productId: 'test-product-id',
            productName: 'Test Product',
            quantity: 2,
            unitPrice: 100,
            total: 200,
          },
        ],
        subtotal: 200,
        taxRate: 0.1,
        taxAmount: 20,
        discountAmount: 0,
        total: 220,
        status: 'draft',
        notes: 'Test invoice notes',
        paymentMethod: 'cash',
        paymentStatus: 'pending',
      };

      const result = await databaseService.createInvoice(invoiceData);
      expect(result).toBeDefined();
      expect(typeof result).toBe('string'); // Should return invoice ID
    });

    test('should get all invoices', async () => {
      const invoices = await databaseService.getAllInvoices();
      expect(Array.isArray(invoices)).toBe(true);
    });

    test('should update invoice status', async () => {
      const invoiceId = 'test-invoice-id';
      const newStatus = 'paid';
      
      await expect(
        databaseService.updateInvoiceStatus(invoiceId, newStatus)
      ).resolves.not.toThrow();
    });
  });

  describe('Purchase Order Management', () => {
    test('should create a new purchase order', async () => {
      const orderData = {
        supplierId: 'test-supplier-id',
        supplierName: 'Test Supplier',
        supplierEmail: '<EMAIL>',
        supplierPhone: '+1234567890',
        supplierAddress: '456 Supplier Street',
        orderDate: new Date().toISOString(),
        expectedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        items: [
          {
            productId: 'test-product-id',
            productName: 'Test Product',
            quantity: 10,
            unitCost: 80,
            total: 800,
          },
        ],
        subtotal: 800,
        taxRate: 0.1,
        taxAmount: 80,
        shippingCost: 50,
        totalAmount: 930,
        status: 'pending',
        notes: 'Test purchase order notes',
        paymentTerms: 'Net 30',
        deliveryAddress: '123 Delivery Address',
      };

      const result = await databaseService.createPurchaseOrder(orderData);
      expect(result).toBeDefined();
      expect(typeof result).toBe('string'); // Should return order ID
    });

    test('should get all purchase orders', async () => {
      const orders = await databaseService.getAllPurchaseOrders();
      expect(Array.isArray(orders)).toBe(true);
    });

    test('should update purchase order status', async () => {
      const orderId = 'test-order-id';
      const newStatus = 'approved';
      
      await expect(
        databaseService.updatePurchaseOrderStatus(orderId, newStatus)
      ).resolves.not.toThrow();
    });
  });

  describe('Financial Management', () => {
    test('should create a new transaction', async () => {
      const transactionData = {
        type: 'income',
        category: 'Sales',
        amount: 1000,
        description: 'Test transaction',
        date: new Date().toISOString(),
        referenceType: 'invoice',
        referenceId: 'test-invoice-id',
      };

      const result = await databaseService.createTransaction(transactionData);
      expect(result).toBeDefined();
      expect(typeof result).toBe('string'); // Should return transaction ID
    });

    test('should get financial summary', async () => {
      const period = 'month';
      const summary = await databaseService.getFinancialSummary(period);
      expect(summary).toBeDefined();
      expect(typeof summary.totalRevenue).toBe('number');
      expect(typeof summary.totalExpenses).toBe('number');
      expect(typeof summary.netProfit).toBe('number');
    });

    test('should get recent transactions', async () => {
      const limit = 10;
      const transactions = await databaseService.getRecentTransactions(limit);
      expect(Array.isArray(transactions)).toBe(true);
      expect(transactions.length).toBeLessThanOrEqual(limit);
    });
  });

  describe('Database Initialization', () => {
    test('should initialize database without errors', async () => {
      await expect(databaseService.initializeDatabase()).resolves.not.toThrow();
    });

    test('should create default user', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'testpassword',
      };

      await expect(
        databaseService.createUser(userData)
      ).resolves.not.toThrow();
    });
  });

  describe('Error Handling', () => {
    test('should handle database connection errors gracefully', async () => {
      // Mock database error
      const mockError = new Error('Database connection failed');
      jest.spyOn(databaseService, 'getAllProducts').mockRejectedValueOnce(mockError);

      await expect(databaseService.getAllProducts()).rejects.toThrow('Database connection failed');
    });

    test('should handle invalid data gracefully', async () => {
      const invalidProductData = {
        // Missing required fields
        name: '',
        unitPrice: -100, // Invalid price
      };

      await expect(
        databaseService.createProduct(invalidProductData as any)
      ).rejects.toThrow();
    });
  });
});
