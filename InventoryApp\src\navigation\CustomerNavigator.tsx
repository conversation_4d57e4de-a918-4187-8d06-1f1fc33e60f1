import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// Import screens
import CustomersScreen from '../screens/customers/CustomersScreen';
import AddCustomerScreen from '../screens/customers/AddCustomerScreen';
import CustomerDetailsScreen from '../screens/customers/CustomerDetailsScreen';

export type CustomerStackParamList = {
  CustomersList: undefined;
  AddCustomer: undefined;
  EditCustomer: { customerId: string };
  CustomerDetails: { customerId: string };
  CustomerInvoices: { customerId: string };
};

const Stack = createStackNavigator<CustomerStackParamList>();

const CustomerNavigator: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Stack.Navigator
      initialRouteName="CustomersList"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen 
        name="CustomersList" 
        component={CustomersScreen}
        options={{
          title: t('navigation.customers'),
        }}
      />
      <Stack.Screen 
        name="AddCustomer" 
        component={AddCustomerScreen}
        options={{
          title: t('customers.addCustomer'),
        }}
      />
      <Stack.Screen 
        name="CustomerDetails" 
        component={CustomerDetailsScreen}
        options={{
          title: t('customers.customerDetails'),
        }}
      />
    </Stack.Navigator>
  );
};

export default CustomerNavigator;
