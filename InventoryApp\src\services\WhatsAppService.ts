import { Linking, Alert, Share } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

export interface WhatsAppMessage {
  phoneNumber: string;
  message: string;
  documentUri?: string;
  documentName?: string;
}

export interface WhatsAppContact {
  name: string;
  phone: string;
  email?: string;
}

class WhatsAppService {
  private static instance: WhatsAppService;

  public static getInstance(): WhatsAppService {
    if (!WhatsAppService.instance) {
      WhatsAppService.instance = new WhatsAppService();
    }
    return WhatsAppService.instance;
  }

  /**
   * Format phone number for WhatsApp
   * Removes all non-numeric characters and ensures proper format
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-numeric characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Add country code if not present (assuming +1 for US/Canada, adjust as needed)
    if (cleaned.length === 10) {
      return `1${cleaned}`;
    }
    
    // Remove leading + if present
    if (cleaned.startsWith('1') && cleaned.length === 11) {
      return cleaned;
    }
    
    return cleaned;
  }

  /**
   * Check if WhatsApp is installed on the device
   */
  async isWhatsAppInstalled(): Promise<boolean> {
    try {
      const whatsappUrl = 'whatsapp://send';
      return await Linking.canOpenURL(whatsappUrl);
    } catch (error) {
      console.error('Error checking WhatsApp availability:', error);
      return false;
    }
  }

  /**
   * Send a text message via WhatsApp
   */
  async sendMessage(phoneNumber: string, message: string): Promise<boolean> {
    try {
      const isInstalled = await this.isWhatsAppInstalled();
      
      if (!isInstalled) {
        Alert.alert(
          'WhatsApp Not Found',
          'WhatsApp is not installed on this device. Please install WhatsApp to send messages.',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Install WhatsApp', 
              onPress: () => this.openWhatsAppStore() 
            }
          ]
        );
        return false;
      }

      const formattedPhone = this.formatPhoneNumber(phoneNumber);
      const encodedMessage = encodeURIComponent(message);
      const whatsappUrl = `whatsapp://send?phone=${formattedPhone}&text=${encodedMessage}`;
      
      await Linking.openURL(whatsappUrl);
      return true;
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      Alert.alert('Error', 'Failed to send WhatsApp message. Please try again.');
      return false;
    }
  }

  /**
   * Send an invoice via WhatsApp
   */
  async sendInvoice(contact: WhatsAppContact, invoice: any, documentUri?: string): Promise<boolean> {
    try {
      const message = this.generateInvoiceMessage(contact, invoice);
      
      if (documentUri) {
        return await this.sendMessageWithDocument(contact.phone, message, documentUri, `Invoice_${invoice.invoiceNumber}.pdf`);
      } else {
        return await this.sendMessage(contact.phone, message);
      }
    } catch (error) {
      console.error('Error sending invoice via WhatsApp:', error);
      Alert.alert('Error', 'Failed to send invoice via WhatsApp. Please try again.');
      return false;
    }
  }

  /**
   * Send a purchase order via WhatsApp
   */
  async sendPurchaseOrder(contact: WhatsAppContact, order: any, documentUri?: string): Promise<boolean> {
    try {
      const message = this.generatePurchaseOrderMessage(contact, order);
      
      if (documentUri) {
        return await this.sendMessageWithDocument(contact.phone, message, documentUri, `PO_${order.orderNumber}.pdf`);
      } else {
        return await this.sendMessage(contact.phone, message);
      }
    } catch (error) {
      console.error('Error sending purchase order via WhatsApp:', error);
      Alert.alert('Error', 'Failed to send purchase order via WhatsApp. Please try again.');
      return false;
    }
  }

  /**
   * Send a message with document attachment
   */
  async sendMessageWithDocument(phoneNumber: string, message: string, documentUri: string, fileName: string): Promise<boolean> {
    try {
      const isInstalled = await this.isWhatsAppInstalled();
      
      if (!isInstalled) {
        Alert.alert(
          'WhatsApp Not Found',
          'WhatsApp is not installed on this device. Please install WhatsApp to send documents.',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Install WhatsApp', 
              onPress: () => this.openWhatsAppStore() 
            }
          ]
        );
        return false;
      }

      // First send the text message
      await this.sendMessage(phoneNumber, message);
      
      // Then share the document
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(documentUri, {
          mimeType: 'application/pdf',
          dialogTitle: `Share ${fileName}`,
          UTI: 'com.adobe.pdf'
        });
        return true;
      } else {
        // Fallback to regular share
        await Share.share({
          url: documentUri,
          title: fileName,
          message: `Please find attached: ${fileName}`
        });
        return true;
      }
    } catch (error) {
      console.error('Error sending document via WhatsApp:', error);
      Alert.alert('Error', 'Failed to send document via WhatsApp. Please try again.');
      return false;
    }
  }

  /**
   * Send customer information via WhatsApp
   */
  async sendCustomerInfo(contact: WhatsAppContact, customerData: any): Promise<boolean> {
    try {
      const message = this.generateCustomerInfoMessage(customerData);
      return await this.sendMessage(contact.phone, message);
    } catch (error) {
      console.error('Error sending customer info via WhatsApp:', error);
      Alert.alert('Error', 'Failed to send customer information via WhatsApp. Please try again.');
      return false;
    }
  }

  /**
   * Send stock alert via WhatsApp
   */
  async sendStockAlert(contact: WhatsAppContact, products: any[]): Promise<boolean> {
    try {
      const message = this.generateStockAlertMessage(products);
      return await this.sendMessage(contact.phone, message);
    } catch (error) {
      console.error('Error sending stock alert via WhatsApp:', error);
      Alert.alert('Error', 'Failed to send stock alert via WhatsApp. Please try again.');
      return false;
    }
  }

  /**
   * Open WhatsApp store for installation
   */
  private async openWhatsAppStore(): Promise<void> {
    try {
      const storeUrl = 'https://play.google.com/store/apps/details?id=com.whatsapp';
      await Linking.openURL(storeUrl);
    } catch (error) {
      console.error('Error opening WhatsApp store:', error);
    }
  }

  /**
   * Generate invoice message template
   */
  private generateInvoiceMessage(contact: WhatsAppContact, invoice: any): string {
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);
    };

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString();
    };

    return `
🧾 *Invoice Details*

Hello ${contact.name},

📋 *Invoice #:* ${invoice.invoiceNumber}
📅 *Date:* ${formatDate(invoice.date)}
📅 *Due Date:* ${formatDate(invoice.dueDate)}

💰 *Amount:* ${formatCurrency(invoice.total)}
📊 *Status:* ${invoice.status.toUpperCase()}

${invoice.items && invoice.items.length > 0 ? `
📦 *Items:*
${invoice.items.map(item => `• ${item.productName} (${item.quantity}x) - ${formatCurrency(item.total)}`).join('\n')}
` : ''}

${invoice.notes ? `📝 *Notes:* ${invoice.notes}` : ''}

Thank you for your business! 🙏

---
Sent via Inventory Management App
    `.trim();
  }

  /**
   * Generate purchase order message template
   */
  private generatePurchaseOrderMessage(contact: WhatsAppContact, order: any): string {
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);
    };

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString();
    };

    return `
📋 *Purchase Order Details*

Hello ${contact.name},

🔢 *PO #:* ${order.orderNumber}
📅 *Order Date:* ${formatDate(order.orderDate)}
📅 *Expected Delivery:* ${formatDate(order.expectedDelivery)}

💰 *Total Amount:* ${formatCurrency(order.totalAmount)}
📊 *Status:* ${order.status.toUpperCase()}

${order.items && order.items.length > 0 ? `
📦 *Items:*
${order.items.map(item => `• ${item.productName} (${item.quantity}x) - ${formatCurrency(item.total)}`).join('\n')}
` : ''}

${order.notes ? `📝 *Notes:* ${order.notes}` : ''}

Please confirm receipt and expected delivery timeline.

---
Sent via Inventory Management App
    `.trim();
  }

  /**
   * Generate customer info message template
   */
  private generateCustomerInfoMessage(customer: any): string {
    return `
👤 *Customer Information*

📝 *Name:* ${customer.name}
📧 *Email:* ${customer.email || 'N/A'}
📞 *Phone:* ${customer.phone || 'N/A'}
🏢 *Type:* ${customer.type || 'Individual'}
📍 *Address:* ${customer.address || 'N/A'}

${customer.notes ? `📝 *Notes:* ${customer.notes}` : ''}

---
Sent via Inventory Management App
    `.trim();
  }

  /**
   * Generate stock alert message template
   */
  private generateStockAlertMessage(products: any[]): string {
    return `
⚠️ *Stock Alert*

The following products are running low on stock:

${products.map(product => `
📦 *${product.name}*
📊 Current Stock: ${product.currentStock}
⚠️ Minimum Stock: ${product.minStock}
`).join('\n')}

Please reorder these items to avoid stockouts.

---
Sent via Inventory Management App
    `.trim();
  }
}

export default WhatsAppService;
