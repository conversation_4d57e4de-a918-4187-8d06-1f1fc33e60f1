import React, { useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../store';
import { checkAuthStatus } from '../store/slices/authSlice';
import { loadAppSettings } from '../store/slices/appSlice';

import AuthNavigator from './AuthNavigator';
import MainNavigator from './MainNavigator';
import LoadingScreen from '../components/LoadingScreen';

const Stack = createStackNavigator();

const AppNavigator: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { isAuthenticated, isLoading: authLoading } = useSelector((state: RootState) => state.auth);
  const { isLoading: appLoading } = useSelector((state: RootState) => state.app);

  useEffect(() => {
    const initializeApp = async () => {
      // Load app settings
      await dispatch(loadAppSettings());
      // Check authentication status
      await dispatch(checkAuthStatus());
    };

    initializeApp();
  }, [dispatch]);

  if (authLoading || appLoading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {isAuthenticated ? (
        <Stack.Screen name="Main" component={MainNavigator} />
      ) : (
        <Stack.Screen name="Auth" component={AuthNavigator} />
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;
