@echo off
title Augment Code Removal - Quick Start

echo ========================================
echo    Augment Code Complete Removal
echo ========================================
echo.
echo Choose your preferred removal method:
echo.
echo 1. PowerShell Script (Recommended - Most Complete)
echo 2. Batch Script (Simple - Easy to Use)
echo 3. Preview Mode (See what will be removed)
echo 4. Verify Removal (Check if already removed)
echo 5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto powershell
if "%choice%"=="2" goto batch
if "%choice%"=="3" goto preview
if "%choice%"=="4" goto verify
if "%choice%"=="5" goto exit

echo Invalid choice. Please try again.
pause
goto start

:powershell
echo.
echo Starting PowerShell removal script...
echo This is the most comprehensive removal method.
echo.
powershell -ExecutionPolicy Bypass -File "remove_augment.ps1"
goto end

:batch
echo.
echo Starting Batch removal script...
echo This is a simple but effective removal method.
echo.
call remove_augment.bat
goto end

:preview
echo.
echo Starting preview mode...
echo This will show you what would be removed without actually removing anything.
echo.
powershell -ExecutionPolicy Bypass -File "remove_augment.ps1" -WhatIf
goto end

:verify
echo.
echo Checking if Augment Code has been completely removed...
echo.
powershell -ExecutionPolicy Bypass -File "verify_removal.ps1" -Detailed
goto end

:exit
echo.
echo Goodbye!
exit /b 0

:end
echo.
echo ========================================
echo Operation completed!
echo ========================================
echo.
echo What to do next:
echo - Restart your computer for all changes to take effect
echo - Run option 4 to verify complete removal
echo - Check the README file for troubleshooting
echo.
pause

:start
cls
goto :eof
