import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  Button,
  Chip,
  Divider,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchFinancialSummary, fetchRecentTransactions } from '../../store/slices/financialSlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const FinancialDashboardScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { summary, recentTransactions, isLoading } = useSelector((state: RootState) => state.financial);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  useEffect(() => {
    loadFinancialData();
  }, [selectedPeriod]);

  const loadFinancialData = async () => {
    try {
      await dispatch(fetchFinancialSummary(selectedPeriod));
      await dispatch(fetchRecentTransactions());
    } catch (error) {
      console.error('Failed to load financial data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFinancialData();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'income':
        return 'trending-up';
      case 'expense':
        return 'trending-down';
      case 'sale':
        return 'point-of-sale';
      case 'purchase':
        return 'shopping-cart';
      default:
        return 'swap-horiz';
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'income':
      case 'sale':
        return colors.tertiary;
      case 'expense':
      case 'purchase':
        return colors.error;
      default:
        return colors.onSurfaceVariant;
    }
  };

  const periods = [
    { value: 'week', label: t('financial.thisWeek') },
    { value: 'month', label: t('financial.thisMonth') },
    { value: 'quarter', label: t('financial.thisQuarter') },
    { value: 'year', label: t('financial.thisYear') },
  ];

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title={t('financial.dashboard')} />
        <Appbar.Action icon="filter-list" onPress={() => {}} />
        <Appbar.Action icon="file-download" onPress={() => navigation.navigate('Reports')} />
      </Appbar.Header>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Period Selection */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('financial.period')}
            </Text>
            <View style={[styles.periodContainer, isRTL && styles.periodContainerRTL]}>
              {periods.map((period) => (
                <Chip
                  key={period.value}
                  mode={selectedPeriod === period.value ? 'flat' : 'outlined'}
                  selected={selectedPeriod === period.value}
                  onPress={() => setSelectedPeriod(period.value)}
                  style={styles.periodChip}
                >
                  {period.label}
                </Chip>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* Financial Summary */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('financial.summary')}
            </Text>

            <View style={[styles.summaryGrid, isRTL && styles.summaryGridRTL]}>
              <View style={styles.summaryItem}>
                <View style={[styles.summaryIcon, { backgroundColor: colors.tertiary + '20' }]}>
                  <Icon name="trending-up" size={24} color={colors.tertiary} />
                </View>
                <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                  {t('financial.totalRevenue')}
                </Text>
                <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL, { color: colors.tertiary }]}>
                  {formatCurrency(summary?.totalRevenue || 0)}
                </Text>
              </View>

              <View style={styles.summaryItem}>
                <View style={[styles.summaryIcon, { backgroundColor: colors.error + '20' }]}>
                  <Icon name="trending-down" size={24} color={colors.error} />
                </View>
                <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                  {t('financial.totalExpenses')}
                </Text>
                <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL, { color: colors.error }]}>
                  {formatCurrency(summary?.totalExpenses || 0)}
                </Text>
              </View>

              <View style={styles.summaryItem}>
                <View style={[styles.summaryIcon, { backgroundColor: colors.primary + '20' }]}>
                  <Icon name="account-balance" size={24} color={colors.primary} />
                </View>
                <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                  {t('financial.netProfit')}
                </Text>
                <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL, { color: colors.primary }]}>
                  {formatCurrency((summary?.totalRevenue || 0) - (summary?.totalExpenses || 0))}
                </Text>
              </View>

              <View style={styles.summaryItem}>
                <View style={[styles.summaryIcon, { backgroundColor: colors.secondary + '20' }]}>
                  <Icon name="point-of-sale" size={24} color={colors.secondary} />
                </View>
                <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                  {t('financial.totalSales')}
                </Text>
                <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL, { color: colors.secondary }]}>
                  {summary?.totalSales || 0}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Quick Actions */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('financial.quickActions')}
            </Text>

            <View style={[styles.actionsGrid, isRTL && styles.actionsGridRTL]}>
              <Button
                mode="outlined"
                onPress={() => navigation.navigate('AddTransaction')}
                style={styles.actionButton}
                icon="add"
              >
                {t('financial.addTransaction')}
              </Button>

              <Button
                mode="outlined"
                onPress={() => navigation.navigate('Reports')}
                style={styles.actionButton}
                icon="assessment"
              >
                {t('financial.viewReports')}
              </Button>

              <Button
                mode="outlined"
                onPress={() => navigation.navigate('Transactions')}
                style={styles.actionButton}
                icon="list"
              >
                {t('financial.allTransactions')}
              </Button>

              <Button
                mode="outlined"
                onPress={() => navigation.navigate('Categories')}
                style={styles.actionButton}
                icon="category"
              >
                {t('financial.categories')}
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Recent Transactions */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={[styles.sectionHeader, isRTL && styles.sectionHeaderRTL]}>
              <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
                {t('financial.recentTransactions')}
              </Text>
              <Button
                mode="text"
                onPress={() => navigation.navigate('Transactions')}
                compact
              >
                {t('common.viewAll')}
              </Button>
            </View>

            {recentTransactions && recentTransactions.length > 0 ? (
              recentTransactions.slice(0, 5).map((transaction, index) => (
                <View key={transaction.id} style={styles.transactionItem}>
                  <View style={[styles.transactionIcon, { backgroundColor: getTransactionColor(transaction.type) + '20' }]}>
                    <Icon 
                      name={getTransactionIcon(transaction.type)} 
                      size={20} 
                      color={getTransactionColor(transaction.type)} 
                    />
                  </View>
                  
                  <View style={[styles.transactionInfo, isRTL && styles.transactionInfoRTL]}>
                    <Text style={[styles.transactionDescription, isRTL && styles.transactionDescriptionRTL]}>
                      {transaction.description}
                    </Text>
                    <Text style={[styles.transactionDate, isRTL && styles.transactionDateRTL]}>
                      {formatDate(transaction.date)}
                    </Text>
                  </View>
                  
                  <Text style={[
                    styles.transactionAmount, 
                    isRTL && styles.transactionAmountRTL,
                    { color: getTransactionColor(transaction.type) }
                  ]}>
                    {transaction.type === 'expense' || transaction.type === 'purchase' ? '-' : '+'}
                    {formatCurrency(Math.abs(transaction.amount))}
                  </Text>
                </View>
              ))
            ) : (
              <View style={styles.emptyState}>
                <Icon name="receipt" size={48} color={colors.onSurfaceVariant} />
                <Text style={[styles.emptyStateText, isRTL && styles.emptyStateTextRTL]}>
                  {t('messages.noTransactions')}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  card: {
    marginBottom: spacing.md,
    borderRadius: 12,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
  },
  sectionTitleRTL: {
    textAlign: 'right',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  periodContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  periodContainerRTL: {
    flexDirection: 'row-reverse',
  },
  periodChip: {
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  summaryGridRTL: {
    flexDirection: 'row-reverse',
  },
  summaryItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: spacing.md,
    padding: spacing.sm,
    backgroundColor: colors.surfaceVariant,
    borderRadius: 8,
  },
  summaryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: 12,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  summaryLabelRTL: {
    textAlign: 'right',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  summaryValueRTL: {
    textAlign: 'right',
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionsGridRTL: {
    flexDirection: 'row-reverse',
  },
  actionButton: {
    width: '48%',
    marginBottom: spacing.sm,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.outline,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionInfoRTL: {
    alignItems: 'flex-end',
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  transactionDescriptionRTL: {
    textAlign: 'right',
  },
  transactionDate: {
    fontSize: 12,
    color: colors.onSurfaceVariant,
  },
  transactionDateRTL: {
    textAlign: 'right',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: spacing.md,
  },
  transactionAmountRTL: {
    marginLeft: 0,
    marginRight: spacing.md,
    textAlign: 'left',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
  },
  emptyStateTextRTL: {
    textAlign: 'right',
  },
});

export default FinancialDashboardScreen;
