# Augment Code Complete Removal Guide

هذا الدليل يوضح كيفية إزالة أداة Augment Code بالكامل من جهازك ومن جميع المشاريع.

## الملفات المتوفرة

### 1. `remove_augment.ps1` (PowerShell Script - الأفضل)
سكريبت PowerShell متقدم يوفر إزالة شاملة مع خيارات متقدمة.

**المميزات:**
- إزالة شاملة من جميع المواقع المحتملة
- فحص العمليات وإيقافها
- تنظيف السجل (Registry)
- إزالة متغيرات البيئة
- تنظيف إضافات VS Code
- فحص وإزالة حزم NPM
- خيار المعاينة (What-If)
- رسائل ملونة وواضحة

### 2. `remove_augment.bat` (Batch File - سهل الاستخدام)
ملف batch بسيط يمكن تشغيله بنقرة واحدة.

**المميزات:**
- سهل التشغيل
- لا يتطلب معرفة تقنية
- إزالة أساسية شاملة
- يعمل على جميع إصدارات Windows

## طرق التشغيل

### الطريقة الأولى: PowerShell Script (موصى بها)

#### 1. التشغيل العادي:
```powershell
.\remove_augment.ps1
```

#### 2. التشغيل مع المعاينة (لرؤية ما سيتم حذفه دون حذف فعلي):
```powershell
.\remove_augment.ps1 -WhatIf
```

#### 3. التشغيل بدون تأكيد:
```powershell
.\remove_augment.ps1 -Force
```

#### 4. التشغيل مع تفاصيل إضافية:
```powershell
.\remove_augment.ps1 -Verbose
```

#### 5. التشغيل كمدير (Administrator):
```powershell
# افتح PowerShell كمدير ثم شغل:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\remove_augment.ps1
```

### الطريقة الثانية: Batch File

#### 1. التشغيل العادي:
- انقر نقرة مزدوجة على `remove_augment.bat`

#### 2. التشغيل كمدير:
- انقر بالزر الأيمن على `remove_augment.bat`
- اختر "Run as administrator"

## ما يتم إزالته

### 1. ملفات النظام:
- `%ProgramFiles%\Augment`
- `%ProgramFiles%\Augment Code`
- `%ProgramFiles(x86)%\Augment`
- `%LOCALAPPDATA%\Augment`
- `%APPDATA%\Augment`

### 2. ملفات المستخدم:
- `%USERPROFILE%\.augment`
- `%USERPROFILE%\.augmentcode`
- ملفات التكوين في مجلد المستخدم

### 3. إضافات VS Code:
- جميع الإضافات التي تحتوي على "augment"
- إزالة عبر VS Code CLI

### 4. ملفات المشاريع:
- `.augment`
- `.augmentcode`
- `augment.json`
- `augment.config.js`
- `augment.config.json`
- مجلدات `node_modules` التي تحتوي على Augment

### 5. السجل (Registry):
- `HKCU\Software\Augment`
- `HKLM\Software\Augment`
- مفاتيح إلغاء التثبيت

### 6. متغيرات البيئة:
- `AUGMENT_HOME`
- `AUGMENT_PATH`
- `AUGMENT_CONFIG`
- إزالة من PATH

### 7. حزم NPM:
- حزم NPM العامة التي تحتوي على "augment"

## استكشاف الأخطاء

### مشكلة: "Execution Policy"
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### مشكلة: "Access Denied"
- شغل الأمر كمدير (Administrator)
- تأكد من إغلاق جميع برامج VS Code و Augment

### مشكلة: بعض الملفات لم تُحذف
- أعد تشغيل الكمبيوتر
- شغل السكريبت مرة أخرى كمدير

### مشكلة: العمليات لا تتوقف
```powershell
# أوقف العمليات يدوياً:
Get-Process | Where-Object {$_.Name -like "*augment*"} | Stop-Process -Force
```

## التحقق من الإزالة الكاملة

### 1. فحص الملفات:
```powershell
# ابحث عن أي ملفات متبقية:
Get-ChildItem -Path C:\ -Recurse -Force -ErrorAction SilentlyContinue | Where-Object { $_.Name -like "*augment*" }
```

### 2. فحص العمليات:
```powershell
Get-Process | Where-Object {$_.Name -like "*augment*"}
```

### 3. فحص السجل:
```powershell
Get-ChildItem -Path "HKCU:\Software" | Where-Object {$_.Name -like "*augment*"}
```

### 4. فحص متغيرات البيئة:
```powershell
Get-ChildItem Env: | Where-Object {$_.Name -like "*augment*"}
```

## ملاحظات مهمة

1. **النسخ الاحتياطي**: تأكد من عمل نسخة احتياطية لمشاريعك المهمة قبل التشغيل
2. **إعادة التشغيل**: أعد تشغيل الكمبيوتر بعد الانتهاء لضمان تطبيق جميع التغييرات
3. **الصلاحيات**: للإزالة الكاملة، شغل السكريبت كمدير
4. **VS Code**: قد تحتاج لإعادة تشغيل VS Code بعد الإزالة

## الدعم

إذا واجهت أي مشاكل:
1. شغل السكريبت مع خيار `-Verbose` لمزيد من التفاصيل
2. تأكد من تشغيله كمدير
3. تحقق من رسائل الخطأ وحاول حلها يدوياً
4. استخدم خيار `-WhatIf` لمعاينة ما سيتم حذفه

## أمان البيانات

هذه السكريبتات تحذف الملفات نهائياً. تأكد من:
- عمل نسخة احتياطية للبيانات المهمة
- مراجعة قائمة الملفات التي ستُحذف
- استخدام خيار `-WhatIf` للمعاينة أولاً
