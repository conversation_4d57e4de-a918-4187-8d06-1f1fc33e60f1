import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Appbar,
  Card,
  RadioButton,
  Switch,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { createCustomer } from '../../store/slices/customerSlice';
import { commonStyles, colors, spacing } from '../../theme';

interface CustomerFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  type: 'individual' | 'business';
  taxNumber: string;
  creditLimit: string;
  paymentTerms: string;
  notes: string;
  isActive: boolean;
}

const AddCustomerScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading } = useSelector((state: RootState) => state.customers);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    country: '',
    type: 'individual',
    taxNumber: '',
    creditLimit: '0',
    paymentTerms: '30',
    notes: '',
    isActive: true,
  });

  const handleInputChange = (field: keyof CustomerFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      Alert.alert(t('validation.error'), t('validation.customerNameRequired'));
      return false;
    }
    if (formData.email && !isValidEmail(formData.email)) {
      Alert.alert(t('validation.error'), t('validation.validEmailRequired'));
      return false;
    }
    if (!formData.phone.trim()) {
      Alert.alert(t('validation.error'), t('validation.phoneRequired'));
      return false;
    }
    return true;
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      const customerData = {
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim(),
        address: formData.address.trim(),
        city: formData.city.trim(),
        country: formData.country.trim(),
        type: formData.type,
        taxNumber: formData.taxNumber.trim(),
        creditLimit: parseFloat(formData.creditLimit) || 0,
        paymentTerms: parseInt(formData.paymentTerms) || 30,
        notes: formData.notes.trim(),
        isActive: formData.isActive,
      };

      await dispatch(createCustomer(customerData));
      Alert.alert(t('messages.success'), t('messages.customerCreated'), [
        { text: t('common.ok'), onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert(t('messages.error'), t('messages.operationFailed'));
    }
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={t('customers.addCustomer')} />
        <Appbar.Action icon="check" onPress={handleSave} />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('customers.basicInfo')}
            </Text>

            <TextInput
              label={t('customers.customerName')}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              style={styles.input}
              mode="outlined"
            />

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('customers.email')}
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="email-address"
              />
              <TextInput
                label={t('customers.phone')}
                value={formData.phone}
                onChangeText={(value) => handleInputChange('phone', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="phone-pad"
              />
            </View>

            <TextInput
              label={t('customers.address')}
              value={formData.address}
              onChangeText={(value) => handleInputChange('address', value)}
              style={styles.input}
              mode="outlined"
              multiline
              numberOfLines={2}
            />

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('customers.city')}
                value={formData.city}
                onChangeText={(value) => handleInputChange('city', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
              <TextInput
                label={t('customers.country')}
                value={formData.country}
                onChangeText={(value) => handleInputChange('country', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
            </View>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('customers.customerType')}
            </Text>

            <RadioButton.Group
              onValueChange={(value) => handleInputChange('type', value)}
              value={formData.type}
            >
              <View style={[styles.radioRow, isRTL && styles.radioRowRTL]}>
                <RadioButton value="individual" />
                <Text style={[styles.radioLabel, isRTL && styles.radioLabelRTL]}>
                  {t('customers.individual')}
                </Text>
              </View>
              <View style={[styles.radioRow, isRTL && styles.radioRowRTL]}>
                <RadioButton value="business" />
                <Text style={[styles.radioLabel, isRTL && styles.radioLabelRTL]}>
                  {t('customers.business')}
                </Text>
              </View>
            </RadioButton.Group>

            {formData.type === 'business' && (
              <TextInput
                label={t('customers.taxNumber')}
                value={formData.taxNumber}
                onChangeText={(value) => handleInputChange('taxNumber', value)}
                style={styles.input}
                mode="outlined"
              />
            )}
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('customers.businessSettings')}
            </Text>

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('customers.creditLimit')}
                value={formData.creditLimit}
                onChangeText={(value) => handleInputChange('creditLimit', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="numeric"
              />
              <TextInput
                label={t('customers.paymentTerms')}
                value={formData.paymentTerms}
                onChangeText={(value) => handleInputChange('paymentTerms', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="numeric"
                right={<TextInput.Affix text={t('customers.days')} />}
              />
            </View>

            <TextInput
              label={t('customers.notes')}
              value={formData.notes}
              onChangeText={(value) => handleInputChange('notes', value)}
              style={styles.input}
              mode="outlined"
              multiline
              numberOfLines={3}
            />

            <View style={[styles.switchRow, isRTL && styles.switchRowRTL]}>
              <Text style={[styles.switchLabel, isRTL && styles.switchLabelRTL]}>
                {t('customers.activeCustomer')}
              </Text>
              <Switch
                value={formData.isActive}
                onValueChange={(value) => handleInputChange('isActive', value)}
                color={colors.primary}
              />
            </View>
          </Card.Content>
        </Card>

        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.saveButton}
          loading={isLoading}
          disabled={isLoading}
        >
          {t('common.save')}
        </Button>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  card: {
    marginBottom: spacing.md,
    borderRadius: 12,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
  },
  sectionTitleRTL: {
    textAlign: 'right',
  },
  input: {
    marginBottom: spacing.sm,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rowRTL: {
    flexDirection: 'row-reverse',
  },
  halfInput: {
    width: '48%',
  },
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  radioRowRTL: {
    flexDirection: 'row-reverse',
  },
  radioLabel: {
    fontSize: 16,
    color: colors.onSurface,
    marginLeft: spacing.sm,
  },
  radioLabelRTL: {
    marginLeft: 0,
    marginRight: spacing.sm,
    textAlign: 'right',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.md,
  },
  switchRowRTL: {
    flexDirection: 'row-reverse',
  },
  switchLabel: {
    fontSize: 16,
    color: colors.onSurface,
  },
  switchLabelRTL: {
    textAlign: 'right',
  },
  saveButton: {
    marginVertical: spacing.lg,
    borderRadius: 8,
  },
});

export default AddCustomerScreen;
