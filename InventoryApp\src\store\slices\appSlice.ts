import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Localization from 'expo-localization';

interface AppState {
  language: 'en' | 'ar';
  theme: 'light' | 'dark';
  isLoading: boolean;
  isRTL: boolean;
  currency: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
}

const initialState: AppState = {
  language: 'en',
  theme: 'light',
  isLoading: false,
  isRTL: false,
  currency: 'USD',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: '24h',
};

// Async thunks
export const loadAppSettings = createAsyncThunk(
  'app/loadAppSettings',
  async (_, { rejectWithValue }) => {
    try {
      const settings = await AsyncStorage.getItem('appSettings');
      if (settings) {
        return JSON.parse(settings);
      }
      
      // Detect device language
      const deviceLanguage = Localization.locale.startsWith('ar') ? 'ar' : 'en';
      return {
        language: deviceLanguage,
        isRTL: deviceLanguage === 'ar',
      };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to load settings');
    }
  }
);

export const saveAppSettings = createAsyncThunk(
  'app/saveAppSettings',
  async (settings: Partial<AppState>, { getState, rejectWithValue }) => {
    try {
      const currentState = (getState() as any).app;
      const newSettings = { ...currentState, ...settings };
      await AsyncStorage.setItem('appSettings', JSON.stringify(newSettings));
      return newSettings;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to save settings');
    }
  }
);

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<'en' | 'ar'>) => {
      state.language = action.payload;
      state.isRTL = action.payload === 'ar';
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    setCurrency: (state, action: PayloadAction<string>) => {
      state.currency = action.payload;
    },
    setDateFormat: (state, action: PayloadAction<string>) => {
      state.dateFormat = action.payload;
    },
    setTimeFormat: (state, action: PayloadAction<'12h' | '24h'>) => {
      state.timeFormat = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadAppSettings.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(loadAppSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        Object.assign(state, action.payload);
      })
      .addCase(loadAppSettings.rejected, (state) => {
        state.isLoading = false;
      })
      .addCase(saveAppSettings.fulfilled, (state, action) => {
        Object.assign(state, action.payload);
      });
  },
});

export const {
  setLanguage,
  setTheme,
  setCurrency,
  setDateFormat,
  setTimeFormat,
  setLoading,
} = appSlice.actions;

export default appSlice.reducer;
