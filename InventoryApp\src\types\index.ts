// Core Types for Inventory Management App

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'manager' | 'employee';
  createdAt: string;
  updatedAt: string;
}

export interface Warehouse {
  id: string;
  name: string;
  location: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  nameAr: string;
  description?: string;
  descriptionAr?: string;
  parentId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Product {
  id: string;
  name: string;
  nameAr: string;
  description?: string;
  descriptionAr?: string;
  sku: string;
  barcode?: string;
  categoryId: string;
  unitPrice: number;
  costPrice: number;
  minStockLevel: number;
  maxStockLevel: number;
  unit: string; // piece, kg, liter, etc.
  isActive: boolean;
  imageUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Stock {
  id: string;
  productId: string;
  warehouseId: string;
  quantity: number;
  cartonQuantity: number;
  piecesPerCarton: number;
  reservedQuantity: number;
  lastUpdated: string;
}

export interface Customer {
  id: string;
  name: string;
  nameAr: string;
  email?: string;
  phone: string;
  whatsapp?: string;
  address: string;
  addressAr: string;
  city: string;
  cityAr: string;
  country: string;
  countryAr: string;
  customerType: 'individual' | 'business';
  taxNumber?: string;
  creditLimit: number;
  paymentTerms: number; // days
  isActive: boolean;
  notes?: string;
  notesAr?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Supplier {
  id: string;
  name: string;
  nameAr: string;
  email?: string;
  phone: string;
  address: string;
  addressAr: string;
  city: string;
  cityAr: string;
  country: string;
  countryAr: string;
  taxNumber?: string;
  paymentTerms: number;
  isActive: boolean;
  notes?: string;
  notesAr?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  customerId: string;
  warehouseId: string;
  invoiceDate: string;
  dueDate: string;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  paymentStatus: 'pending' | 'partial' | 'paid';
  notes?: string;
  notesAr?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceItem {
  id: string;
  invoiceId: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  discountPercent: number;
  taxPercent: number;
  totalAmount: number;
}

export interface PurchaseOrder {
  id: string;
  poNumber: string;
  supplierId: string;
  warehouseId: string;
  orderDate: string;
  expectedDate: string;
  receivedDate?: string;
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  status: 'draft' | 'sent' | 'confirmed' | 'received' | 'cancelled';
  notes?: string;
  notesAr?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseOrderItem {
  id: string;
  purchaseOrderId: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  receivedQuantity: number;
  totalAmount: number;
}

export interface Transaction {
  id: string;
  type: 'sale' | 'purchase' | 'adjustment' | 'transfer';
  referenceId: string; // invoice id, po id, etc.
  amount: number;
  description: string;
  descriptionAr: string;
  date: string;
  createdAt: string;
}

export interface StockMovement {
  id: string;
  productId: string;
  warehouseId: string;
  type: 'in' | 'out' | 'adjustment' | 'transfer';
  quantity: number;
  referenceType: 'invoice' | 'purchase_order' | 'adjustment' | 'transfer';
  referenceId: string;
  notes?: string;
  notesAr?: string;
  createdAt: string;
}

// Navigation Types
export type RootStackParamList = {
  Login: undefined;
  Main: undefined;
  Dashboard: undefined;
  Inventory: undefined;
  Customers: undefined;
  Invoices: undefined;
  PurchaseOrders: undefined;
  Reports: undefined;
  Settings: undefined;
};

// Form Types
export interface LoginForm {
  username: string;
  password: string;
}

export interface ProductForm {
  name: string;
  nameAr: string;
  description?: string;
  descriptionAr?: string;
  sku: string;
  barcode?: string;
  categoryId: string;
  unitPrice: number;
  costPrice: number;
  minStockLevel: number;
  maxStockLevel: number;
  unit: string;
}

export interface CustomerForm {
  name: string;
  nameAr: string;
  email?: string;
  phone: string;
  whatsapp?: string;
  address: string;
  addressAr: string;
  city: string;
  cityAr: string;
  country: string;
  countryAr: string;
  customerType: 'individual' | 'business';
  taxNumber?: string;
  creditLimit: number;
  paymentTerms: number;
  notes?: string;
  notesAr?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  messageAr?: string;
  errors?: string[];
}

// App State Types
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  language: 'en' | 'ar';
  theme: 'light' | 'dark';
  isLoading: boolean;
}
