import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { configureStore } from '@reduxjs/toolkit';
import App from '../../App';
import DatabaseService from '../../src/database/DatabaseService';
import appSlice from '../../src/store/slices/appSlice';
import inventorySlice from '../../src/store/slices/inventorySlice';
import customerSlice from '../../src/store/slices/customerSlice';
import invoiceSlice from '../../src/store/slices/invoiceSlice';

// Mock database service
jest.mock('../../src/database/DatabaseService');

// Mock navigation
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  NavigationContainer: ({ children }: any) => children,
}));

// Mock i18n
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: jest.fn(),
      language: 'en',
    },
  }),
}));

const mockStore = configureStore({
  reducer: {
    app: appSlice,
    inventory: inventorySlice,
    customers: customerSlice,
    invoices: invoiceSlice,
  },
});

const MockedDatabaseService = DatabaseService as jest.MockedClass<typeof DatabaseService>;

describe('App Integration Flow', () => {
  let mockDatabaseService: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockDatabaseService = {
      initializeDatabase: jest.fn().mockResolvedValue(undefined),
      getAllProducts: jest.fn().mockResolvedValue([]),
      getAllCustomers: jest.fn().mockResolvedValue([]),
      getAllInvoices: jest.fn().mockResolvedValue([]),
      createProduct: jest.fn().mockResolvedValue('product-id'),
      createCustomer: jest.fn().mockResolvedValue('customer-id'),
      createInvoice: jest.fn().mockResolvedValue('invoice-id'),
      getFinancialSummary: jest.fn().mockResolvedValue({
        totalRevenue: 10000,
        totalExpenses: 5000,
        netProfit: 5000,
      }),
    };

    MockedDatabaseService.getInstance.mockReturnValue(mockDatabaseService);
  });

  const renderApp = () => {
    return render(
      <Provider store={mockStore}>
        <NavigationContainer>
          <App />
        </NavigationContainer>
      </Provider>
    );
  };

  describe('App Initialization', () => {
    test('should initialize database on app start', async () => {
      renderApp();

      await waitFor(() => {
        expect(mockDatabaseService.initializeDatabase).toHaveBeenCalled();
      });
    });

    test('should load initial data', async () => {
      renderApp();

      await waitFor(() => {
        expect(mockDatabaseService.getAllProducts).toHaveBeenCalled();
        expect(mockDatabaseService.getAllCustomers).toHaveBeenCalled();
        expect(mockDatabaseService.getAllInvoices).toHaveBeenCalled();
      });
    });
  });

  describe('Product Management Flow', () => {
    test('should create and display new product', async () => {
      const mockProduct = {
        id: 'product-1',
        name: 'Test Product',
        sku: 'TEST001',
        unitPrice: 100,
        stockLevel: 50,
      };

      mockDatabaseService.getAllProducts.mockResolvedValueOnce([mockProduct]);

      const { getByText, getByPlaceholderText } = renderApp();

      // Navigate to inventory
      fireEvent.press(getByText('inventory.title'));

      // Add new product
      fireEvent.press(getByText('common.add'));

      // Fill product form
      fireEvent.changeText(getByPlaceholderText('product.name'), 'Test Product');
      fireEvent.changeText(getByPlaceholderText('product.sku'), 'TEST001');
      fireEvent.changeText(getByPlaceholderText('product.unitPrice'), '100');
      fireEvent.changeText(getByPlaceholderText('product.stockLevel'), '50');

      // Save product
      fireEvent.press(getByText('common.save'));

      await waitFor(() => {
        expect(mockDatabaseService.createProduct).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'Test Product',
            sku: 'TEST001',
            unitPrice: 100,
            stockLevel: 50,
          })
        );
      });
    });

    test('should handle low stock alerts', async () => {
      const lowStockProduct = {
        id: 'product-1',
        name: 'Low Stock Product',
        stockLevel: 5,
        minStock: 10,
      };

      mockDatabaseService.getAllProducts.mockResolvedValueOnce([lowStockProduct]);

      const { getByText } = renderApp();

      // Navigate to inventory
      fireEvent.press(getByText('inventory.title'));

      await waitFor(() => {
        expect(getByText('Low Stock Product')).toBeTruthy();
        // Should show low stock indicator
      });
    });
  });

  describe('Customer Management Flow', () => {
    test('should create and manage customers', async () => {
      const mockCustomer = {
        id: 'customer-1',
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '+1234567890',
      };

      mockDatabaseService.getAllCustomers.mockResolvedValueOnce([mockCustomer]);

      const { getByText, getByPlaceholderText } = renderApp();

      // Navigate to customers
      fireEvent.press(getByText('customers.title'));

      // Add new customer
      fireEvent.press(getByText('common.add'));

      // Fill customer form
      fireEvent.changeText(getByPlaceholderText('customer.name'), 'Test Customer');
      fireEvent.changeText(getByPlaceholderText('customer.email'), '<EMAIL>');
      fireEvent.changeText(getByPlaceholderText('customer.phone'), '+1234567890');

      // Save customer
      fireEvent.press(getByText('common.save'));

      await waitFor(() => {
        expect(mockDatabaseService.createCustomer).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'Test Customer',
            email: '<EMAIL>',
            phone: '+1234567890',
          })
        );
      });
    });
  });

  describe('Invoice Generation Flow', () => {
    test('should create invoice with products and customer', async () => {
      const mockProduct = {
        id: 'product-1',
        name: 'Test Product',
        unitPrice: 100,
        stockLevel: 50,
      };

      const mockCustomer = {
        id: 'customer-1',
        name: 'Test Customer',
        email: '<EMAIL>',
      };

      mockDatabaseService.getAllProducts.mockResolvedValue([mockProduct]);
      mockDatabaseService.getAllCustomers.mockResolvedValue([mockCustomer]);

      const { getByText } = renderApp();

      // Navigate to invoices
      fireEvent.press(getByText('invoices.title'));

      // Create new invoice
      fireEvent.press(getByText('common.add'));

      // Select customer
      fireEvent.press(getByText('Test Customer'));

      // Add product to invoice
      fireEvent.press(getByText('invoice.addItem'));
      fireEvent.press(getByText('Test Product'));

      // Set quantity
      fireEvent.changeText(getByPlaceholderText('invoice.quantity'), '2');

      // Save invoice
      fireEvent.press(getByText('common.save'));

      await waitFor(() => {
        expect(mockDatabaseService.createInvoice).toHaveBeenCalledWith(
          expect.objectContaining({
            customerId: 'customer-1',
            items: expect.arrayContaining([
              expect.objectContaining({
                productId: 'product-1',
                quantity: 2,
                unitPrice: 100,
                total: 200,
              }),
            ]),
            total: expect.any(Number),
          })
        );
      });
    });

    test('should calculate invoice totals correctly', async () => {
      const mockProduct = {
        id: 'product-1',
        name: 'Test Product',
        unitPrice: 100,
      };

      mockDatabaseService.getAllProducts.mockResolvedValue([mockProduct]);

      const { getByText, getByDisplayValue } = renderApp();

      // Navigate to invoices and create new invoice
      fireEvent.press(getByText('invoices.title'));
      fireEvent.press(getByText('common.add'));

      // Add product with quantity 2
      fireEvent.press(getByText('invoice.addItem'));
      fireEvent.press(getByText('Test Product'));
      fireEvent.changeText(getByPlaceholderText('invoice.quantity'), '2');

      // Set tax rate to 10%
      fireEvent.changeText(getByPlaceholderText('invoice.taxRate'), '10');

      await waitFor(() => {
        // Subtotal should be 200 (2 * 100)
        expect(getByDisplayValue('200.00')).toBeTruthy();
        // Tax should be 20 (200 * 0.1)
        expect(getByDisplayValue('20.00')).toBeTruthy();
        // Total should be 220 (200 + 20)
        expect(getByDisplayValue('220.00')).toBeTruthy();
      });
    });
  });

  describe('Financial Dashboard Flow', () => {
    test('should display financial summary', async () => {
      const { getByText } = renderApp();

      // Navigate to financial dashboard
      fireEvent.press(getByText('financial.dashboard'));

      await waitFor(() => {
        expect(mockDatabaseService.getFinancialSummary).toHaveBeenCalled();
        expect(getByText('10,000.00')).toBeTruthy(); // Total revenue
        expect(getByText('5,000.00')).toBeTruthy(); // Total expenses
        expect(getByText('5,000.00')).toBeTruthy(); // Net profit
      });
    });
  });

  describe('Language and RTL Support', () => {
    test('should switch between English and Arabic', async () => {
      const { getByText } = renderApp();

      // Open settings
      fireEvent.press(getByText('settings.title'));

      // Switch to Arabic
      fireEvent.press(getByText('settings.language'));
      fireEvent.press(getByText('العربية'));

      await waitFor(() => {
        // Should display Arabic text
        expect(getByText('المخزون')).toBeTruthy();
      });

      // Switch back to English
      fireEvent.press(getByText('الإعدادات'));
      fireEvent.press(getByText('اللغة'));
      fireEvent.press(getByText('English'));

      await waitFor(() => {
        // Should display English text
        expect(getByText('Inventory')).toBeTruthy();
      });
    });

    test('should apply RTL layout for Arabic', async () => {
      const { getByTestId } = renderApp();

      // Switch to Arabic
      fireEvent.press(getByText('settings.title'));
      fireEvent.press(getByText('settings.language'));
      fireEvent.press(getByText('العربية'));

      await waitFor(() => {
        // Check if RTL styles are applied
        const container = getByTestId('main-container');
        expect(container.props.style).toEqual(
          expect.objectContaining({
            direction: 'rtl',
          })
        );
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle database errors gracefully', async () => {
      mockDatabaseService.getAllProducts.mockRejectedValueOnce(
        new Error('Database connection failed')
      );

      const { getByText } = renderApp();

      await waitFor(() => {
        expect(getByText('messages.databaseError')).toBeTruthy();
      });
    });

    test('should handle network errors', async () => {
      mockDatabaseService.createProduct.mockRejectedValueOnce(
        new Error('Network error')
      );

      const { getByText, getByPlaceholderText } = renderApp();

      // Try to create a product
      fireEvent.press(getByText('inventory.title'));
      fireEvent.press(getByText('common.add'));
      fireEvent.changeText(getByPlaceholderText('product.name'), 'Test Product');
      fireEvent.press(getByText('common.save'));

      await waitFor(() => {
        expect(getByText('messages.networkError')).toBeTruthy();
      });
    });
  });

  describe('Data Persistence', () => {
    test('should persist data across app restarts', async () => {
      const mockProducts = [
        { id: '1', name: 'Product 1' },
        { id: '2', name: 'Product 2' },
      ];

      mockDatabaseService.getAllProducts.mockResolvedValue(mockProducts);

      const { getByText, unmount } = renderApp();

      // Wait for data to load
      await waitFor(() => {
        expect(getByText('Product 1')).toBeTruthy();
        expect(getByText('Product 2')).toBeTruthy();
      });

      // Unmount and remount app (simulate restart)
      unmount();
      const { getByText: getByTextAfterRestart } = renderApp();

      // Data should still be available
      await waitFor(() => {
        expect(getByTextAfterRestart('Product 1')).toBeTruthy();
        expect(getByTextAfterRestart('Product 2')).toBeTruthy();
      });
    });
  });

  describe('Performance', () => {
    test('should handle large datasets efficiently', async () => {
      const largeProductList = Array.from({ length: 1000 }, (_, i) => ({
        id: `product-${i}`,
        name: `Product ${i}`,
        unitPrice: 100 + i,
        stockLevel: 50 + i,
      }));

      mockDatabaseService.getAllProducts.mockResolvedValue(largeProductList);

      const startTime = Date.now();
      const { getByText } = renderApp();

      // Navigate to inventory
      fireEvent.press(getByText('inventory.title'));

      await waitFor(() => {
        expect(getByText('Product 0')).toBeTruthy();
      });

      const endTime = Date.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (less than 2 seconds)
      expect(renderTime).toBeLessThan(2000);
    });
  });
});
