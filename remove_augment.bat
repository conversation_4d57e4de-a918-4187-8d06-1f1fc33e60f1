@echo off
setlocal enabledelayedexpansion

:: Augment Code Complete Removal Script (Batch Version)
:: This script removes Augment Code from your system and all projects

title Augment Code Removal Tool

echo ========================================
echo Augment Code Complete Removal Script
echo ========================================
echo.

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with Administrator privileges...
) else (
    echo WARNING: Not running as Administrator. Some operations may fail.
    echo For complete removal, right-click and "Run as Administrator"
    echo.
)

:: Confirmation
set /p confirm="This will completely remove Augment Code from your system. Continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting removal process...
echo.

:: Stop Augment processes
echo ========================================
echo Stopping Augment Processes
echo ========================================
taskkill /f /im "augment*" >nul 2>&1
taskkill /f /im "*augment*" >nul 2>&1
echo Augment processes stopped.

:: Remove from Program Files
echo.
echo ========================================
echo Removing from Program Files
echo ========================================
if exist "%ProgramFiles%\Augment" (
    echo Removing %ProgramFiles%\Augment
    rmdir /s /q "%ProgramFiles%\Augment" >nul 2>&1
)
if exist "%ProgramFiles%\Augment Code" (
    echo Removing %ProgramFiles%\Augment Code
    rmdir /s /q "%ProgramFiles%\Augment Code" >nul 2>&1
)
if exist "%ProgramFiles(x86)%\Augment" (
    echo Removing %ProgramFiles(x86)%\Augment
    rmdir /s /q "%ProgramFiles(x86)%\Augment" >nul 2>&1
)
if exist "%ProgramFiles(x86)%\Augment Code" (
    echo Removing %ProgramFiles(x86)%\Augment Code
    rmdir /s /q "%ProgramFiles(x86)%\Augment Code" >nul 2>&1
)

:: Remove from User Profile
echo.
echo ========================================
echo Removing from User Profile
echo ========================================
if exist "%USERPROFILE%\.augment" (
    echo Removing %USERPROFILE%\.augment
    rmdir /s /q "%USERPROFILE%\.augment" >nul 2>&1
)
if exist "%USERPROFILE%\.augmentcode" (
    echo Removing %USERPROFILE%\.augmentcode
    rmdir /s /q "%USERPROFILE%\.augmentcode" >nul 2>&1
)
if exist "%LOCALAPPDATA%\Augment" (
    echo Removing %LOCALAPPDATA%\Augment
    rmdir /s /q "%LOCALAPPDATA%\Augment" >nul 2>&1
)
if exist "%LOCALAPPDATA%\AugmentCode" (
    echo Removing %LOCALAPPDATA%\AugmentCode
    rmdir /s /q "%LOCALAPPDATA%\AugmentCode" >nul 2>&1
)
if exist "%APPDATA%\Augment" (
    echo Removing %APPDATA%\Augment
    rmdir /s /q "%APPDATA%\Augment" >nul 2>&1
)
if exist "%APPDATA%\AugmentCode" (
    echo Removing %APPDATA%\AugmentCode
    rmdir /s /q "%APPDATA%\AugmentCode" >nul 2>&1
)

:: Remove VS Code extensions
echo.
echo ========================================
echo Removing VS Code Extensions
echo ========================================
if exist "%USERPROFILE%\.vscode\extensions" (
    for /d %%i in ("%USERPROFILE%\.vscode\extensions\*augment*") do (
        echo Removing VS Code extension: %%i
        rmdir /s /q "%%i" >nul 2>&1
    )
)

:: Try to uninstall via code command
where code >nul 2>&1
if %errorlevel% == 0 (
    echo Attempting to uninstall via VS Code CLI...
    for /f "tokens=*" %%i in ('code --list-extensions ^| findstr /i augment') do (
        echo Uninstalling extension: %%i
        code --uninstall-extension "%%i"
    )
)

:: Remove from common project directories
echo.
echo ========================================
echo Removing from Projects
echo ========================================
set "project_dirs=%USERPROFILE%\Documents %USERPROFILE%\Desktop %USERPROFILE%\source %USERPROFILE%\repos %USERPROFILE%\projects C:\projects C:\source C:\repos"

for %%d in (%project_dirs%) do (
    if exist "%%d" (
        echo Scanning: %%d
        for /r "%%d" %%f in (.augment .augmentcode augment.json augment.config.js augment.config.json .augment.json) do (
            if exist "%%f" (
                echo Removing: %%f
                del /f /q "%%f" >nul 2>&1
            )
        )
        for /r "%%d" %%f in (node_modules) do (
            if exist "%%f" (
                for /d %%g in ("%%f\*augment*") do (
                    echo Removing: %%g
                    rmdir /s /q "%%g" >nul 2>&1
                )
            )
        )
    )
)

:: Clean NPM global packages
echo.
echo ========================================
echo Cleaning NPM Packages
echo ========================================
where npm >nul 2>&1
if %errorlevel% == 0 (
    echo Checking NPM global packages...
    for /f "tokens=*" %%i in ('npm list -g --depth=0 ^| findstr /i augment') do (
        for /f "tokens=1" %%j in ("%%i") do (
            echo Uninstalling NPM package: %%j
            npm uninstall -g "%%j"
        )
    )
)

:: Registry cleanup (basic)
echo.
echo ========================================
echo Registry Cleanup
echo ========================================
echo Removing registry entries...
reg delete "HKCU\Software\Augment" /f >nul 2>&1
reg delete "HKCU\Software\AugmentCode" /f >nul 2>&1
reg delete "HKLM\Software\Augment" /f >nul 2>&1
reg delete "HKLM\Software\AugmentCode" /f >nul 2>&1

:: Environment variables cleanup
echo.
echo ========================================
echo Environment Variables Cleanup
echo ========================================
setx AUGMENT_HOME "" >nul 2>&1
setx AUGMENT_PATH "" >nul 2>&1
setx AUGMENT_CONFIG "" >nul 2>&1

echo.
echo ========================================
echo Removal Complete
echo ========================================
echo Augment Code has been removed from your system!
echo.
echo IMPORTANT NOTES:
echo - Restart your computer for all changes to take effect
echo - Some registry entries may require manual removal
echo - Check your projects for any remaining Augment-specific files
echo - If you encounter issues, run this script as Administrator
echo.
echo For complete PowerShell-based removal, run: remove_augment.ps1
echo.
pause
