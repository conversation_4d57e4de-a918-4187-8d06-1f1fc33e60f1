{"name": "inventoryapp", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --coverage --watchAll=false", "test:unit": "jest --testPathIgnorePatterns=integration", "test:integration": "jest --testPathPattern=integration", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "test:all": "./scripts/test.sh"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "@reduxjs/toolkit": "^2.8.2", "expo": "~51.0.0", "expo-document-picker": "^13.1.6", "expo-image-picker": "^16.1.4", "expo-linking": "^7.1.7", "expo-localization": "^16.1.6", "expo-print": "^14.1.4", "expo-sharing": "^13.1.5", "expo-sqlite": "^15.2.14", "expo-status-bar": "~2.2.3", "i18next": "^25.3.2", "react": "18.2.0", "react-i18next": "^15.6.1", "react-native": "0.74.2", "react-native-chart-kit": "^6.12.0", "react-native-fs": "^2.20.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.14.5", "react-native-pdf-lib": "^1.0.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-super-grid": "^6.0.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.3.0", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.9.0", "@types/jest": "^29.5.8", "@types/react": "~18.2.45", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.55.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.5", "jest-expo": "~51.0.0", "react-test-renderer": "18.2.0", "typescript": "~5.8.3"}, "private": true}