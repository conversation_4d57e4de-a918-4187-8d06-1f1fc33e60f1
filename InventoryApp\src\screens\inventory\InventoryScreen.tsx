import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  FAB,
  Searchbar,
  Chip,
  Badge,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchProducts, fetchStock } from '../../store/slices/inventorySlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const InventoryScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { products, stock, isLoading } = useSelector((state: RootState) => state.inventory);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [searchQuery, setSearchQuery] = React.useState('');
  const [refreshing, setRefreshing] = React.useState(false);

  useEffect(() => {
    loadInventoryData();
  }, []);

  const loadInventoryData = async () => {
    try {
      await Promise.all([
        dispatch(fetchProducts()),
        dispatch(fetchStock()),
      ]);
    } catch (error) {
      console.error('Failed to load inventory data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInventoryData();
    setRefreshing(false);
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.sku?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.barcode?.includes(searchQuery)
  );

  const getStockLevel = (productId: string) => {
    const productStock = stock.find(s => s.productId === productId);
    return productStock ? productStock.quantity : 0;
  };

  const getStockStatus = (productId: string, minStock: number) => {
    const currentStock = getStockLevel(productId);
    if (currentStock === 0) return 'outOfStock';
    if (currentStock <= minStock) return 'lowStock';
    return 'inStock';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'inStock':
        return colors.tertiary;
      case 'lowStock':
        return colors.secondary;
      case 'outOfStock':
        return colors.error;
      default:
        return colors.onSurfaceVariant;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'inStock':
        return t('inventory.inStock');
      case 'lowStock':
        return t('inventory.lowStock');
      case 'outOfStock':
        return t('inventory.outOfStock');
      default:
        return '';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const renderProductItem = ({ item }: { item: any }) => {
    const stockLevel = getStockLevel(item.id);
    const stockStatus = getStockStatus(item.id, item.minStock || 10);
    
    return (
      <Card style={styles.productCard}>
        <Card.Content>
          <View style={[styles.productHeader, isRTL && styles.productHeaderRTL]}>
            <View style={styles.productInfo}>
              <Text style={[styles.productName, isRTL && styles.productNameRTL]}>
                {item.name}
              </Text>
              <Text style={[styles.productSku, isRTL && styles.productSkuRTL]}>
                {item.sku}
              </Text>
            </View>
            <Chip
              mode="outlined"
              compact
              style={[styles.statusChip, { borderColor: getStatusColor(stockStatus) }]}
              textStyle={{ color: getStatusColor(stockStatus) }}
            >
              {getStatusText(stockStatus)}
            </Chip>
          </View>
          
          <View style={styles.productDetails}>
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Icon name="inventory" size={16} color={colors.onSurfaceVariant} />
              <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
                {t('inventory.stockLevel')}: {stockLevel} {item.unit || 'pcs'}
              </Text>
            </View>
            
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Icon name="attach-money" size={16} color={colors.onSurfaceVariant} />
              <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
                {formatCurrency(item.unitPrice || 0)}
              </Text>
            </View>

            {item.category && (
              <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
                <Icon name="category" size={16} color={colors.onSurfaceVariant} />
                <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
                  {item.category}
                </Text>
              </View>
            )}
          </View>

          <View style={[styles.productActions, isRTL && styles.productActionsRTL]}>
            <Chip
              icon="edit"
              mode="outlined"
              compact
              onPress={() => {}}
              style={styles.actionChip}
            >
              {t('common.edit')}
            </Chip>
            <Chip
              icon="add"
              mode="outlined"
              compact
              onPress={() => {}}
              style={styles.actionChip}
            >
              {t('inventory.stockAdjustment')}
            </Chip>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="inventory" size={64} color={colors.onSurfaceVariant} />
      <Text style={[styles.emptyStateText, isRTL && styles.emptyStateTextRTL]}>
        {t('messages.noData')}
      </Text>
      <Text style={[styles.emptyStateSubtext, isRTL && styles.emptyStateSubtextRTL]}>
        {t('inventory.addProduct')}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title={t('inventory.title')} />
        <Appbar.Action icon="category" onPress={() => navigation.navigate('Categories')} />
        <Appbar.Action icon="swap-horiz" onPress={() => navigation.navigate('StockMovement')} />
        <Appbar.Action icon="filter-list" onPress={() => {}} />
      </Appbar.Header>

      <View style={styles.content}>
        <Searchbar
          placeholder={t('common.search')}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />

        <FlatList
          data={filteredProducts}
          renderItem={renderProductItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <FAB
        icon="plus"
        style={[styles.fab, isRTL && styles.fabRTL]}
        onPress={() => navigation.navigate('AddProduct')}
        label={t('inventory.addProduct')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  listContent: {
    paddingBottom: 100,
  },
  productCard: {
    marginBottom: spacing.sm,
    borderRadius: 12,
    elevation: 2,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  productHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  productNameRTL: {
    textAlign: 'right',
  },
  productSku: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
  },
  productSkuRTL: {
    textAlign: 'right',
  },
  statusChip: {
    marginLeft: spacing.sm,
  },
  productDetails: {
    marginVertical: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailRowRTL: {
    flexDirection: 'row-reverse',
  },
  detailText: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    marginLeft: spacing.sm,
    flex: 1,
  },
  detailTextRTL: {
    marginLeft: 0,
    marginRight: spacing.sm,
    textAlign: 'right',
  },
  productActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: spacing.sm,
  },
  productActionsRTL: {
    flexDirection: 'row-reverse',
    justifyContent: 'flex-start',
  },
  actionChip: {
    marginLeft: spacing.sm,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
    marginBottom: spacing.xs,
  },
  emptyStateTextRTL: {
    textAlign: 'right',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
  emptyStateSubtextRTL: {
    textAlign: 'right',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  fabRTL: {
    right: undefined,
    left: 0,
  },
});

export default InventoryScreen;
