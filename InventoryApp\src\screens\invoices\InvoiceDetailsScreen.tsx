import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Share,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  Button,
  Chip,
  Divider,
  List,
  Menu,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchInvoiceById, updateInvoiceStatus, deleteInvoice } from '../../store/slices/invoiceSlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';
import PDFService from '../../services/PDFService';
import WhatsAppButton from '../../components/WhatsAppButton';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';

const InvoiceDetailsScreen: React.FC<{ navigation: any; route: any }> = ({ navigation, route }) => {
  const { invoiceId } = route.params;
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { invoices, isLoading } = useSelector((state: RootState) => state.invoices);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [invoice, setInvoice] = useState<any>(null);
  const [statusMenuVisible, setStatusMenuVisible] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const statusOptions = [
    { value: 'draft', label: t('invoices.draft'), color: colors.onSurfaceVariant },
    { value: 'sent', label: t('invoices.sent'), color: colors.primary },
    { value: 'paid', label: t('invoices.paid'), color: colors.tertiary },
    { value: 'overdue', label: t('invoices.overdue'), color: colors.error },
    { value: 'cancelled', label: t('invoices.cancelled'), color: colors.error },
  ];

  useEffect(() => {
    loadInvoice();
  }, [invoiceId]);

  const loadInvoice = async () => {
    try {
      const invoiceData = invoices.find(i => i.id === invoiceId);
      if (invoiceData) {
        setInvoice(invoiceData);
      } else {
        await dispatch(fetchInvoiceById(invoiceId));
      }
    } catch (error) {
      console.error('Failed to load invoice:', error);
      Alert.alert(t('messages.error'), t('messages.invoiceNotFound'));
      navigation.goBack();
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    try {
      await dispatch(updateInvoiceStatus({ id: invoiceId, status: newStatus }));
      setInvoice(prev => ({ ...prev, status: newStatus }));
      setStatusMenuVisible(false);
      Alert.alert(t('messages.success'), t('messages.invoiceStatusUpdated'));
    } catch (error) {
      Alert.alert(t('messages.error'), t('messages.operationFailed'));
    }
  };

  const generatePDF = async () => {
    if (!invoice) return;

    setIsGeneratingPDF(true);
    try {
      const pdfService = PDFService.getInstance();

      const pdfData = {
        invoiceNumber: invoice.invoiceNumber,
        date: invoice.date,
        dueDate: invoice.dueDate,
        customer: {
          name: invoice.customerName,
          email: invoice.customerEmail,
          phone: invoice.customerPhone,
          address: invoice.customerAddress,
        },
        company: {
          name: 'Inventory Management Company',
          address: '123 Business Street, City, Country',
          phone: '+****************',
          email: '<EMAIL>',
        },
        items: invoice.items || [],
        subtotal: invoice.subtotal,
        taxAmount: invoice.taxAmount,
        discountAmount: invoice.discountAmount || 0,
        total: invoice.total,
        notes: invoice.notes,
        status: invoice.status,
      };

      const options = {
        title: `Invoice ${invoice.invoiceNumber}`,
        filename: `Invoice_${invoice.invoiceNumber}`,
        isRTL: isRTL,
      };

      const pdfUri = await pdfService.generateInvoicePDF(pdfData, options);
      await pdfService.sharePDF(pdfUri, `Invoice ${invoice.invoiceNumber}`);
    } catch (error) {
      console.error('Error generating PDF:', error);
      Alert.alert(t('messages.error'), t('messages.pdfGenerationFailed'));
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const printInvoice = async () => {
    if (!invoice) return;

    try {
      const pdfService = PDFService.getInstance();

      const pdfData = {
        invoiceNumber: invoice.invoiceNumber,
        date: invoice.date,
        dueDate: invoice.dueDate,
        customer: {
          name: invoice.customerName,
          email: invoice.customerEmail,
          phone: invoice.customerPhone,
          address: invoice.customerAddress,
        },
        company: {
          name: 'Inventory Management Company',
          address: '123 Business Street, City, Country',
          phone: '+****************',
          email: '<EMAIL>',
        },
        items: invoice.items || [],
        subtotal: invoice.subtotal,
        taxAmount: invoice.taxAmount,
        discountAmount: invoice.discountAmount || 0,
        total: invoice.total,
        notes: invoice.notes,
        status: invoice.status,
      };

      const options = {
        title: `Invoice ${invoice.invoiceNumber}`,
        filename: `Invoice_${invoice.invoiceNumber}`,
        isRTL: isRTL,
      };

      const pdfUri = await pdfService.generateInvoicePDF(pdfData, options);
      await pdfService.printPDF(pdfUri);
    } catch (error) {
      console.error('Error printing invoice:', error);
      Alert.alert(t('messages.error'), t('messages.printFailed'));
    }
  };

  const handleDelete = () => {
    Alert.alert(
      t('common.confirm'),
      t('messages.deleteInvoiceConfirm', { number: invoice?.invoiceNumber }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await dispatch(deleteInvoice(invoiceId));
              Alert.alert(t('messages.success'), t('messages.invoiceDeleted'));
              navigation.goBack();
            } catch (error) {
              Alert.alert(t('messages.error'), t('messages.operationFailed'));
            }
          },
        },
      ]
    );
  };

  const generatePDF = async () => {
    if (!invoice) return;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Invoice ${invoice.invoiceNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .invoice-details { margin-bottom: 20px; }
          .customer-details { margin-bottom: 20px; }
          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          .items-table th { background-color: #f2f2f2; }
          .summary { text-align: right; }
          .total { font-weight: bold; font-size: 18px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>INVOICE</h1>
          <h2>${invoice.invoiceNumber}</h2>
        </div>
        
        <div class="invoice-details">
          <p><strong>Date:</strong> ${new Date(invoice.date).toLocaleDateString()}</p>
          <p><strong>Due Date:</strong> ${new Date(invoice.dueDate).toLocaleDateString()}</p>
          <p><strong>Status:</strong> ${invoice.status.toUpperCase()}</p>
        </div>
        
        <div class="customer-details">
          <h3>Bill To:</h3>
          <p><strong>${invoice.customerName}</strong></p>
          <p>${invoice.customerEmail || ''}</p>
          <p>${invoice.customerPhone || ''}</p>
        </div>
        
        <table class="items-table">
          <thead>
            <tr>
              <th>Item</th>
              <th>Quantity</th>
              <th>Unit Price</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.items.map(item => `
              <tr>
                <td>${item.productName}</td>
                <td>${item.quantity}</td>
                <td>$${item.unitPrice.toFixed(2)}</td>
                <td>$${item.total.toFixed(2)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        
        <div class="summary">
          <p>Subtotal: $${invoice.subtotal.toFixed(2)}</p>
          <p>Tax: $${invoice.taxAmount.toFixed(2)}</p>
          <p>Discount: -$${invoice.discountAmount?.toFixed(2) || '0.00'}</p>
          <p class="total">Total: $${invoice.total.toFixed(2)}</p>
        </div>
        
        ${invoice.notes ? `<div><h3>Notes:</h3><p>${invoice.notes}</p></div>` : ''}
      </body>
      </html>
    `;

    try {
      const { uri } = await Print.printToFileAsync({ html: htmlContent });
      await Sharing.shareAsync(uri);
    } catch (error) {
      Alert.alert(t('messages.error'), t('messages.pdfGenerationFailed'));
    }
  };

  const shareInvoice = async () => {
    if (!invoice) return;

    const message = `Invoice ${invoice.invoiceNumber}\nTotal: ${formatCurrency(invoice.total)}\nDue Date: ${formatDate(invoice.dueDate)}`;
    
    try {
      await Share.share({
        message,
        title: `Invoice ${invoice.invoiceNumber}`,
      });
    } catch (error) {
      console.error('Error sharing invoice:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    const statusOption = statusOptions.find(s => s.value === status);
    return statusOption ? statusOption.color : colors.onSurfaceVariant;
  };

  const getStatusLabel = (status: string) => {
    const statusOption = statusOptions.find(s => s.value === status);
    return statusOption ? statusOption.label : status;
  };

  if (!invoice) {
    return (
      <View style={styles.container}>
        <Appbar.Header>
          <Appbar.BackAction onPress={() => navigation.goBack()} />
          <Appbar.Content title={t('invoices.invoiceDetails')} />
        </Appbar.Header>
        <View style={styles.loadingContainer}>
          <Text>{t('common.loading')}</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={invoice.invoiceNumber} />
        <Appbar.Action icon="share" onPress={shareInvoice} />
        <Appbar.Action icon="picture-as-pdf" onPress={generatePDF} />
        <Appbar.Action icon="print" onPress={printInvoice} />
        <Appbar.Action icon="delete" onPress={handleDelete} />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        {/* Invoice Header */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={[styles.invoiceHeader, isRTL && styles.invoiceHeaderRTL]}>
              <View style={styles.invoiceInfo}>
                <Text style={[styles.invoiceNumber, isRTL && styles.invoiceNumberRTL]}>
                  {invoice.invoiceNumber}
                </Text>
                <Text style={[styles.invoiceDate, isRTL && styles.invoiceDateRTL]}>
                  {formatDate(invoice.date)}
                </Text>
              </View>
              <Menu
                visible={statusMenuVisible}
                onDismiss={() => setStatusMenuVisible(false)}
                anchor={
                  <Chip
                    mode="outlined"
                    style={[styles.statusChip, { borderColor: getStatusColor(invoice.status) }]}
                    textStyle={{ color: getStatusColor(invoice.status) }}
                    onPress={() => setStatusMenuVisible(true)}
                  >
                    {getStatusLabel(invoice.status)}
                  </Chip>
                }
              >
                {statusOptions.map((status) => (
                  <Menu.Item
                    key={status.value}
                    onPress={() => handleStatusChange(status.value)}
                    title={status.label}
                  />
                ))}
              </Menu>
            </View>
          </Card.Content>
        </Card>

        {/* Customer Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('invoices.billTo')}
            </Text>
            <Text style={[styles.customerName, isRTL && styles.customerNameRTL]}>
              {invoice.customerName}
            </Text>
            {invoice.customerEmail && (
              <Text style={[styles.customerDetail, isRTL && styles.customerDetailRTL]}>
                {invoice.customerEmail}
              </Text>
            )}
            {invoice.customerPhone && (
              <Text style={[styles.customerDetail, isRTL && styles.customerDetailRTL]}>
                {invoice.customerPhone}
              </Text>
            )}
          </Card.Content>
        </Card>

        {/* Invoice Items */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('invoices.items')}
            </Text>
            
            {invoice.items.map((item, index) => (
              <View key={index} style={styles.itemRow}>
                <View style={[styles.itemInfo, isRTL && styles.itemInfoRTL]}>
                  <Text style={[styles.itemName, isRTL && styles.itemNameRTL]}>
                    {item.productName}
                  </Text>
                  <Text style={[styles.itemDetails, isRTL && styles.itemDetailsRTL]}>
                    {item.quantity} × {formatCurrency(item.unitPrice)}
                  </Text>
                </View>
                <Text style={[styles.itemTotal, isRTL && styles.itemTotalRTL]}>
                  {formatCurrency(item.total)}
                </Text>
              </View>
            ))}
          </Card.Content>
        </Card>

        {/* Invoice Summary */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('invoices.summary')}
            </Text>

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                {t('invoices.subtotal')}:
              </Text>
              <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL]}>
                {formatCurrency(invoice.subtotal)}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                {t('invoices.tax')}:
              </Text>
              <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL]}>
                {formatCurrency(invoice.taxAmount)}
              </Text>
            </View>

            {invoice.discountAmount > 0 && (
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                  {t('invoices.discount')}:
                </Text>
                <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL]}>
                  -{formatCurrency(invoice.discountAmount)}
                </Text>
              </View>
            )}

            <Divider style={styles.divider} />

            <View style={styles.summaryRow}>
              <Text style={[styles.totalLabel, isRTL && styles.totalLabelRTL]}>
                {t('invoices.total')}:
              </Text>
              <Text style={[styles.totalValue, isRTL && styles.totalValueRTL]}>
                {formatCurrency(invoice.total)}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                {t('invoices.dueDate')}:
              </Text>
              <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL]}>
                {formatDate(invoice.dueDate)}
              </Text>
            </View>

            {invoice.notes && (
              <View style={styles.notesSection}>
                <Text style={[styles.notesTitle, isRTL && styles.notesTitleRTL]}>
                  {t('invoices.notes')}:
                </Text>
                <Text style={[styles.notesText, isRTL && styles.notesTextRTL]}>
                  {invoice.notes}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.actionButtons}>
              <Button
                mode="contained"
                onPress={generatePDF}
                style={styles.actionButton}
                icon="picture-as-pdf"
              >
                {t('invoices.downloadPDF')}
              </Button>
              <Button
                mode="outlined"
                onPress={shareInvoice}
                style={styles.actionButton}
                icon="share"
              >
                {t('common.share')}
              </Button>
              <Button
                mode="outlined"
                onPress={() => navigation.navigate('EditInvoice', { invoiceId })}
                style={styles.actionButton}
                icon="edit"
              >
                {t('common.edit')}
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Actions Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('common.actions')}
            </Text>
            <View style={[styles.actionsContainer, isRTL && styles.actionsContainerRTL]}>
              <WhatsAppButton
                contact={{
                  name: invoice.customerName,
                  phone: invoice.customerPhone || '',
                  email: invoice.customerEmail,
                }}
                data={invoice}
                type="invoice"
                mode="contained"
                style={styles.actionButton}
              />
              <Button
                mode="outlined"
                onPress={generatePDF}
                loading={isGeneratingPDF}
                disabled={isGeneratingPDF}
                icon="picture-as-pdf"
                style={styles.actionButton}
              >
                {t('common.generatePDF')}
              </Button>
              <Button
                mode="outlined"
                onPress={printInvoice}
                icon="print"
                style={styles.actionButton}
              >
                {t('common.print')}
              </Button>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    marginBottom: spacing.md,
    borderRadius: 12,
    elevation: 2,
  },
  invoiceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  invoiceHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  invoiceInfo: {
    flex: 1,
  },
  invoiceNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  invoiceNumberRTL: {
    textAlign: 'right',
  },
  invoiceDate: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
  },
  invoiceDateRTL: {
    textAlign: 'right',
  },
  statusChip: {
    marginLeft: spacing.sm,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
  },
  sectionTitleRTL: {
    textAlign: 'right',
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  customerNameRTL: {
    textAlign: 'right',
  },
  customerDetail: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    marginBottom: spacing.xs,
  },
  customerDetailRTL: {
    textAlign: 'right',
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.outline,
  },
  itemInfo: {
    flex: 1,
  },
  itemInfoRTL: {
    alignItems: 'flex-end',
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  itemNameRTL: {
    textAlign: 'right',
  },
  itemDetails: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
  },
  itemDetailsRTL: {
    textAlign: 'right',
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
    marginLeft: spacing.md,
  },
  itemTotalRTL: {
    marginLeft: 0,
    marginRight: spacing.md,
    textAlign: 'left',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: 16,
    color: colors.onSurface,
  },
  summaryLabelRTL: {
    textAlign: 'right',
  },
  summaryValue: {
    fontSize: 16,
    color: colors.onSurface,
  },
  summaryValueRTL: {
    textAlign: 'left',
  },
  divider: {
    marginVertical: spacing.md,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  totalLabelRTL: {
    textAlign: 'right',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  totalValueRTL: {
    textAlign: 'left',
  },
  notesSection: {
    marginTop: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.surfaceVariant,
    borderRadius: 8,
  },
  notesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurfaceVariant,
    marginBottom: spacing.sm,
  },
  notesTitleRTL: {
    textAlign: 'right',
  },
  notesText: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    lineHeight: 20,
  },
  notesTextRTL: {
    textAlign: 'right',
  },
  actionButtons: {
    flexDirection: 'column',
  },
  actionButton: {
    marginBottom: spacing.sm,
    flex: 1,
    minWidth: 120,
    marginHorizontal: spacing.xs,
  },
  actionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginTop: spacing.sm,
  },
  actionsContainerRTL: {
    flexDirection: 'row-reverse',
  },
});

export default InvoiceDetailsScreen;
