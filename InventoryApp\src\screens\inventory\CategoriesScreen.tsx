import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  FAB,
  Searchbar,
  IconButton,
  Dialog,
  Portal,
  TextInput,
  Button,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchCategories, createCategory, updateCategory, deleteCategory } from '../../store/slices/inventorySlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const CategoriesScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { categories, isLoading } = useSelector((state: RootState) => state.inventory);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<any>(null);
  const [categoryName, setCategoryName] = useState('');
  const [categoryDescription, setCategoryDescription] = useState('');

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      await dispatch(fetchCategories());
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCategories();
    setRefreshing(false);
  };

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const openDialog = (category?: any) => {
    if (category) {
      setEditingCategory(category);
      setCategoryName(category.name);
      setCategoryDescription(category.description || '');
    } else {
      setEditingCategory(null);
      setCategoryName('');
      setCategoryDescription('');
    }
    setDialogVisible(true);
  };

  const closeDialog = () => {
    setDialogVisible(false);
    setEditingCategory(null);
    setCategoryName('');
    setCategoryDescription('');
  };

  const handleSave = async () => {
    if (!categoryName.trim()) {
      Alert.alert(t('validation.error'), t('validation.categoryNameRequired'));
      return;
    }

    try {
      const categoryData = {
        name: categoryName.trim(),
        description: categoryDescription.trim(),
      };

      if (editingCategory) {
        await dispatch(updateCategory({ id: editingCategory.id, ...categoryData }));
        Alert.alert(t('messages.success'), t('messages.categoryUpdated'));
      } else {
        await dispatch(createCategory(categoryData));
        Alert.alert(t('messages.success'), t('messages.categoryCreated'));
      }
      
      closeDialog();
      await loadCategories();
    } catch (error) {
      Alert.alert(t('messages.error'), t('messages.operationFailed'));
    }
  };

  const handleDelete = (category: any) => {
    Alert.alert(
      t('common.confirm'),
      t('messages.deleteCategoryConfirm', { name: category.name }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await dispatch(deleteCategory(category.id));
              Alert.alert(t('messages.success'), t('messages.categoryDeleted'));
              await loadCategories();
            } catch (error) {
              Alert.alert(t('messages.error'), t('messages.operationFailed'));
            }
          },
        },
      ]
    );
  };

  const renderCategoryItem = ({ item }: { item: any }) => (
    <Card style={styles.categoryCard}>
      <Card.Content>
        <View style={[styles.categoryHeader, isRTL && styles.categoryHeaderRTL]}>
          <View style={styles.categoryInfo}>
            <Text style={[styles.categoryName, isRTL && styles.categoryNameRTL]}>
              {item.name}
            </Text>
            {item.description && (
              <Text style={[styles.categoryDescription, isRTL && styles.categoryDescriptionRTL]}>
                {item.description}
              </Text>
            )}
          </View>
          <View style={[styles.categoryActions, isRTL && styles.categoryActionsRTL]}>
            <IconButton
              icon="edit"
              size={20}
              onPress={() => openDialog(item)}
            />
            <IconButton
              icon="delete"
              size={20}
              iconColor={colors.error}
              onPress={() => handleDelete(item)}
            />
          </View>
        </View>
        
        <View style={[styles.categoryStats, isRTL && styles.categoryStatsRTL]}>
          <View style={[styles.statItem, isRTL && styles.statItemRTL]}>
            <Icon name="inventory" size={16} color={colors.onSurfaceVariant} />
            <Text style={[styles.statText, isRTL && styles.statTextRTL]}>
              {item.productCount || 0} {t('inventory.products')}
            </Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="category" size={64} color={colors.onSurfaceVariant} />
      <Text style={[styles.emptyStateText, isRTL && styles.emptyStateTextRTL]}>
        {t('messages.noCategories')}
      </Text>
      <Text style={[styles.emptyStateSubtext, isRTL && styles.emptyStateSubtextRTL]}>
        {t('inventory.addCategory')}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={t('inventory.categories')} />
      </Appbar.Header>

      <View style={styles.content}>
        <Searchbar
          placeholder={t('common.search')}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />

        <FlatList
          data={filteredCategories}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <FAB
        icon="plus"
        style={[styles.fab, isRTL && styles.fabRTL]}
        onPress={() => openDialog()}
        label={t('inventory.addCategory')}
      />

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={closeDialog}>
          <Dialog.Title>
            {editingCategory ? t('inventory.editCategory') : t('inventory.addCategory')}
          </Dialog.Title>
          <Dialog.Content>
            <TextInput
              label={t('inventory.categoryName')}
              value={categoryName}
              onChangeText={setCategoryName}
              style={styles.dialogInput}
              mode="outlined"
            />
            <TextInput
              label={t('inventory.description')}
              value={categoryDescription}
              onChangeText={setCategoryDescription}
              style={styles.dialogInput}
              mode="outlined"
              multiline
              numberOfLines={3}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={closeDialog}>{t('common.cancel')}</Button>
            <Button onPress={handleSave} mode="contained">
              {t('common.save')}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  listContent: {
    paddingBottom: 100,
  },
  categoryCard: {
    marginBottom: spacing.sm,
    borderRadius: 12,
    elevation: 2,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  categoryHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  categoryNameRTL: {
    textAlign: 'right',
  },
  categoryDescription: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
  },
  categoryDescriptionRTL: {
    textAlign: 'right',
  },
  categoryActions: {
    flexDirection: 'row',
  },
  categoryActionsRTL: {
    flexDirection: 'row-reverse',
  },
  categoryStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryStatsRTL: {
    flexDirection: 'row-reverse',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItemRTL: {
    flexDirection: 'row-reverse',
  },
  statText: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    marginLeft: spacing.xs,
  },
  statTextRTL: {
    marginLeft: 0,
    marginRight: spacing.xs,
    textAlign: 'right',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
    marginBottom: spacing.xs,
  },
  emptyStateTextRTL: {
    textAlign: 'right',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
  emptyStateSubtextRTL: {
    textAlign: 'right',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  fabRTL: {
    right: undefined,
    left: 0,
  },
  dialogInput: {
    marginBottom: spacing.sm,
  },
});

export default CategoriesScreen;
