import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Appbar,
  Card,
  List,
  IconButton,
  Menu,
  Divider,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { createInvoice } from '../../store/slices/invoiceSlice';
import { fetchCustomers } from '../../store/slices/customerSlice';
import { fetchProducts } from '../../store/slices/inventorySlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface InvoiceItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface InvoiceFormData {
  customerId: string;
  invoiceNumber: string;
  date: string;
  dueDate: string;
  items: InvoiceItem[];
  notes: string;
  taxRate: number;
  discountAmount: number;
}

const CreateInvoiceScreen: React.FC<{ navigation: any; route: any }> = ({ navigation, route }) => {
  const { customerId: preselectedCustomerId } = route.params || {};
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { customers } = useSelector((state: RootState) => state.customers);
  const { products } = useSelector((state: RootState) => state.inventory);
  const { isLoading } = useSelector((state: RootState) => state.invoices);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [customerMenuVisible, setCustomerMenuVisible] = useState(false);
  const [productMenuVisible, setProductMenuVisible] = useState(false);

  const [formData, setFormData] = useState<InvoiceFormData>({
    customerId: preselectedCustomerId || '',
    invoiceNumber: generateInvoiceNumber(),
    date: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    items: [],
    notes: '',
    taxRate: 10,
    discountAmount: 0,
  });

  function generateInvoiceNumber(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `INV-${year}${month}${day}-${random}`;
  }

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        dispatch(fetchCustomers()),
        dispatch(fetchProducts()),
      ]);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  const addItem = (product: any) => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      productId: product.id,
      productName: product.name,
      quantity: 1,
      unitPrice: product.unitPrice || 0,
      total: product.unitPrice || 0,
    };

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
    setProductMenuVisible(false);
  };

  const updateItem = (itemId: string, field: keyof InvoiceItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === itemId) {
          const updatedItem = { ...item, [field]: value };
          if (field === 'quantity' || field === 'unitPrice') {
            updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
          }
          return updatedItem;
        }
        return item;
      }),
    }));
  };

  const removeItem = (itemId: string) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId),
    }));
  };

  const calculateSubtotal = (): number => {
    return formData.items.reduce((sum, item) => sum + item.total, 0);
  };

  const calculateTax = (): number => {
    return (calculateSubtotal() * formData.taxRate) / 100;
  };

  const calculateTotal = (): number => {
    return calculateSubtotal() + calculateTax() - formData.discountAmount;
  };

  const validateForm = (): boolean => {
    if (!formData.customerId) {
      Alert.alert(t('validation.error'), t('validation.customerRequired'));
      return false;
    }
    if (formData.items.length === 0) {
      Alert.alert(t('validation.error'), t('validation.invoiceItemsRequired'));
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      const invoiceData = {
        ...formData,
        subtotal: calculateSubtotal(),
        taxAmount: calculateTax(),
        total: calculateTotal(),
        status: 'draft',
      };

      await dispatch(createInvoice(invoiceData));
      Alert.alert(t('messages.success'), t('messages.invoiceCreated'), [
        { text: t('common.ok'), onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert(t('messages.error'), t('messages.operationFailed'));
    }
  };

  const selectedCustomer = customers.find(c => c.id === formData.customerId);
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={t('invoices.createInvoice')} />
        <Appbar.Action icon="check" onPress={handleSave} />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        {/* Invoice Header */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('invoices.invoiceDetails')}
            </Text>

            <TextInput
              label={t('invoices.invoiceNumber')}
              value={formData.invoiceNumber}
              onChangeText={(value) => setFormData(prev => ({ ...prev, invoiceNumber: value }))}
              style={styles.input}
              mode="outlined"
            />

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('invoices.date')}
                value={formData.date}
                onChangeText={(value) => setFormData(prev => ({ ...prev, date: value }))}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
              <TextInput
                label={t('invoices.dueDate')}
                value={formData.dueDate}
                onChangeText={(value) => setFormData(prev => ({ ...prev, dueDate: value }))}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
            </View>

            <Menu
              visible={customerMenuVisible}
              onDismiss={() => setCustomerMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setCustomerMenuVisible(true)}
                  style={styles.menuButton}
                >
                  {selectedCustomer ? selectedCustomer.name : t('invoices.selectCustomer')}
                </Button>
              }
            >
              {customers.map((customer) => (
                <Menu.Item
                  key={customer.id}
                  onPress={() => {
                    setFormData(prev => ({ ...prev, customerId: customer.id }));
                    setCustomerMenuVisible(false);
                  }}
                  title={customer.name}
                />
              ))}
            </Menu>
          </Card.Content>
        </Card>

        {/* Invoice Items */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={[styles.itemsHeader, isRTL && styles.itemsHeaderRTL]}>
              <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
                {t('invoices.items')}
              </Text>
              <Menu
                visible={productMenuVisible}
                onDismiss={() => setProductMenuVisible(false)}
                anchor={
                  <IconButton
                    icon="plus"
                    mode="contained"
                    onPress={() => setProductMenuVisible(true)}
                  />
                }
              >
                {products.map((product) => (
                  <Menu.Item
                    key={product.id}
                    onPress={() => addItem(product)}
                    title={`${product.name} - ${formatCurrency(product.unitPrice || 0)}`}
                  />
                ))}
              </Menu>
            </View>

            {formData.items.map((item) => (
              <Card key={item.id} style={styles.itemCard}>
                <Card.Content>
                  <View style={[styles.itemHeader, isRTL && styles.itemHeaderRTL]}>
                    <Text style={[styles.itemName, isRTL && styles.itemNameRTL]}>
                      {item.productName}
                    </Text>
                    <IconButton
                      icon="delete"
                      size={20}
                      iconColor={colors.error}
                      onPress={() => removeItem(item.id)}
                    />
                  </View>

                  <View style={[styles.itemDetails, isRTL && styles.itemDetailsRTL]}>
                    <TextInput
                      label={t('invoices.quantity')}
                      value={item.quantity.toString()}
                      onChangeText={(value) => updateItem(item.id, 'quantity', parseInt(value) || 0)}
                      style={styles.itemInput}
                      mode="outlined"
                      keyboardType="numeric"
                    />
                    <TextInput
                      label={t('invoices.unitPrice')}
                      value={item.unitPrice.toString()}
                      onChangeText={(value) => updateItem(item.id, 'unitPrice', parseFloat(value) || 0)}
                      style={styles.itemInput}
                      mode="outlined"
                      keyboardType="numeric"
                    />
                    <Text style={[styles.itemTotal, isRTL && styles.itemTotalRTL]}>
                      {formatCurrency(item.total)}
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            ))}

            {formData.items.length === 0 && (
              <View style={styles.emptyItems}>
                <Icon name="receipt" size={48} color={colors.onSurfaceVariant} />
                <Text style={[styles.emptyItemsText, isRTL && styles.emptyItemsTextRTL]}>
                  {t('invoices.noItemsAdded')}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Invoice Summary */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('invoices.summary')}
            </Text>

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('invoices.taxRate')}
                value={formData.taxRate.toString()}
                onChangeText={(value) => setFormData(prev => ({ ...prev, taxRate: parseFloat(value) || 0 }))}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="numeric"
                right={<TextInput.Affix text="%" />}
              />
              <TextInput
                label={t('invoices.discount')}
                value={formData.discountAmount.toString()}
                onChangeText={(value) => setFormData(prev => ({ ...prev, discountAmount: parseFloat(value) || 0 }))}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="numeric"
              />
            </View>

            <Divider style={styles.divider} />

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                {t('invoices.subtotal')}:
              </Text>
              <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL]}>
                {formatCurrency(calculateSubtotal())}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                {t('invoices.tax')}:
              </Text>
              <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL]}>
                {formatCurrency(calculateTax())}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, isRTL && styles.summaryLabelRTL]}>
                {t('invoices.discount')}:
              </Text>
              <Text style={[styles.summaryValue, isRTL && styles.summaryValueRTL]}>
                -{formatCurrency(formData.discountAmount)}
              </Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.summaryRow}>
              <Text style={[styles.totalLabel, isRTL && styles.totalLabelRTL]}>
                {t('invoices.total')}:
              </Text>
              <Text style={[styles.totalValue, isRTL && styles.totalValueRTL]}>
                {formatCurrency(calculateTotal())}
              </Text>
            </View>

            <TextInput
              label={t('invoices.notes')}
              value={formData.notes}
              onChangeText={(value) => setFormData(prev => ({ ...prev, notes: value }))}
              style={styles.input}
              mode="outlined"
              multiline
              numberOfLines={3}
            />
          </Card.Content>
        </Card>

        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.saveButton}
          loading={isLoading}
          disabled={isLoading}
        >
          {t('invoices.createInvoice')}
        </Button>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  card: {
    marginBottom: spacing.md,
    borderRadius: 12,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
  },
  sectionTitleRTL: {
    textAlign: 'right',
  },
  input: {
    marginBottom: spacing.sm,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rowRTL: {
    flexDirection: 'row-reverse',
  },
  halfInput: {
    width: '48%',
  },
  menuButton: {
    marginBottom: spacing.sm,
    justifyContent: 'flex-start',
  },
  itemsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  itemsHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  itemCard: {
    marginBottom: spacing.sm,
    backgroundColor: colors.surfaceVariant,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  itemHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurface,
    flex: 1,
  },
  itemNameRTL: {
    textAlign: 'right',
  },
  itemDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemDetailsRTL: {
    flexDirection: 'row-reverse',
  },
  itemInput: {
    width: '30%',
    marginRight: spacing.sm,
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
    width: '25%',
    textAlign: 'right',
  },
  itemTotalRTL: {
    textAlign: 'left',
  },
  emptyItems: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyItemsText: {
    fontSize: 16,
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
  },
  emptyItemsTextRTL: {
    textAlign: 'right',
  },
  divider: {
    marginVertical: spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: 16,
    color: colors.onSurface,
  },
  summaryLabelRTL: {
    textAlign: 'right',
  },
  summaryValue: {
    fontSize: 16,
    color: colors.onSurface,
  },
  summaryValueRTL: {
    textAlign: 'left',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  totalLabelRTL: {
    textAlign: 'right',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  totalValueRTL: {
    textAlign: 'left',
  },
  saveButton: {
    marginVertical: spacing.lg,
    borderRadius: 8,
  },
});

export default CreateInvoiceScreen;
