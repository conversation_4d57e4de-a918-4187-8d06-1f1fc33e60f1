import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  FAB,
  Searchbar,
  Chip,
  Dialog,
  Portal,
  TextInput,
  Button,
  Menu,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchStockMovements, createStockMovement, fetchProducts } from '../../store/slices/inventorySlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const StockMovementScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { stockMovements, products, isLoading } = useSelector((state: RootState) => state.inventory);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [productMenuVisible, setProductMenuVisible] = useState(false);
  const [typeMenuVisible, setTypeMenuVisible] = useState(false);

  const [formData, setFormData] = useState({
    productId: '',
    type: 'in',
    quantity: '',
    reason: '',
    notes: '',
  });

  const movementTypes = [
    { value: 'in', label: t('inventory.stockIn') },
    { value: 'out', label: t('inventory.stockOut') },
    { value: 'adjustment', label: t('inventory.adjustment') },
    { value: 'transfer', label: t('inventory.transfer') },
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        dispatch(fetchStockMovements()),
        dispatch(fetchProducts()),
      ]);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const filteredMovements = stockMovements.filter(movement =>
    movement.productName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    movement.reason?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const openDialog = () => {
    setFormData({
      productId: '',
      type: 'in',
      quantity: '',
      reason: '',
      notes: '',
    });
    setDialogVisible(true);
  };

  const closeDialog = () => {
    setDialogVisible(false);
  };

  const handleSave = async () => {
    if (!formData.productId) {
      Alert.alert(t('validation.error'), t('validation.productRequired'));
      return;
    }
    if (!formData.quantity || isNaN(parseInt(formData.quantity))) {
      Alert.alert(t('validation.error'), t('validation.validQuantityRequired'));
      return;
    }
    if (!formData.reason.trim()) {
      Alert.alert(t('validation.error'), t('validation.reasonRequired'));
      return;
    }

    try {
      const movementData = {
        productId: formData.productId,
        type: formData.type,
        quantity: parseInt(formData.quantity),
        reason: formData.reason.trim(),
        notes: formData.notes.trim(),
      };

      await dispatch(createStockMovement(movementData));
      Alert.alert(t('messages.success'), t('messages.stockMovementCreated'));
      closeDialog();
      await loadData();
    } catch (error) {
      Alert.alert(t('messages.error'), t('messages.operationFailed'));
    }
  };

  const getMovementTypeColor = (type: string) => {
    switch (type) {
      case 'in':
        return colors.tertiary;
      case 'out':
        return colors.error;
      case 'adjustment':
        return colors.secondary;
      case 'transfer':
        return colors.primary;
      default:
        return colors.onSurfaceVariant;
    }
  };

  const getMovementTypeText = (type: string) => {
    const typeObj = movementTypes.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const renderMovementItem = ({ item }: { item: any }) => (
    <Card style={styles.movementCard}>
      <Card.Content>
        <View style={[styles.movementHeader, isRTL && styles.movementHeaderRTL]}>
          <View style={styles.movementInfo}>
            <Text style={[styles.productName, isRTL && styles.productNameRTL]}>
              {item.productName}
            </Text>
            <Text style={[styles.movementDate, isRTL && styles.movementDateRTL]}>
              {formatDate(item.createdAt)}
            </Text>
          </View>
          <Chip
            mode="outlined"
            compact
            style={[styles.typeChip, { borderColor: getMovementTypeColor(item.type) }]}
            textStyle={{ color: getMovementTypeColor(item.type) }}
          >
            {getMovementTypeText(item.type)}
          </Chip>
        </View>
        
        <View style={styles.movementDetails}>
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Icon name="swap-horiz" size={16} color={colors.onSurfaceVariant} />
            <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
              {item.type === 'out' ? '-' : '+'}{item.quantity} {item.unit || 'pcs'}
            </Text>
          </View>
          
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Icon name="info" size={16} color={colors.onSurfaceVariant} />
            <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
              {item.reason}
            </Text>
          </View>

          {item.notes && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Icon name="note" size={16} color={colors.onSurfaceVariant} />
              <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
                {item.notes}
              </Text>
            </View>
          )}
        </View>
      </Card.Content>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="swap-horiz" size={64} color={colors.onSurfaceVariant} />
      <Text style={[styles.emptyStateText, isRTL && styles.emptyStateTextRTL]}>
        {t('messages.noStockMovements')}
      </Text>
      <Text style={[styles.emptyStateSubtext, isRTL && styles.emptyStateSubtextRTL]}>
        {t('inventory.addStockMovement')}
      </Text>
    </View>
  );

  const selectedProduct = products.find(p => p.id === formData.productId);
  const selectedType = movementTypes.find(t => t.value === formData.type);

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={t('inventory.stockMovements')} />
      </Appbar.Header>

      <View style={styles.content}>
        <Searchbar
          placeholder={t('common.search')}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />

        <FlatList
          data={filteredMovements}
          renderItem={renderMovementItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <FAB
        icon="plus"
        style={[styles.fab, isRTL && styles.fabRTL]}
        onPress={openDialog}
        label={t('inventory.addMovement')}
      />

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={closeDialog}>
          <Dialog.Title>{t('inventory.addStockMovement')}</Dialog.Title>
          <Dialog.Content>
            <Menu
              visible={productMenuVisible}
              onDismiss={() => setProductMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setProductMenuVisible(true)}
                  style={styles.menuButton}
                >
                  {selectedProduct ? selectedProduct.name : t('inventory.selectProduct')}
                </Button>
              }
            >
              {products.map((product) => (
                <Menu.Item
                  key={product.id}
                  onPress={() => {
                    setFormData(prev => ({ ...prev, productId: product.id }));
                    setProductMenuVisible(false);
                  }}
                  title={product.name}
                />
              ))}
            </Menu>

            <Menu
              visible={typeMenuVisible}
              onDismiss={() => setTypeMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setTypeMenuVisible(true)}
                  style={styles.menuButton}
                >
                  {selectedType ? selectedType.label : t('inventory.selectType')}
                </Button>
              }
            >
              {movementTypes.map((type) => (
                <Menu.Item
                  key={type.value}
                  onPress={() => {
                    setFormData(prev => ({ ...prev, type: type.value }));
                    setTypeMenuVisible(false);
                  }}
                  title={type.label}
                />
              ))}
            </Menu>

            <TextInput
              label={t('inventory.quantity')}
              value={formData.quantity}
              onChangeText={(value) => setFormData(prev => ({ ...prev, quantity: value }))}
              style={styles.dialogInput}
              mode="outlined"
              keyboardType="numeric"
            />

            <TextInput
              label={t('inventory.reason')}
              value={formData.reason}
              onChangeText={(value) => setFormData(prev => ({ ...prev, reason: value }))}
              style={styles.dialogInput}
              mode="outlined"
            />

            <TextInput
              label={t('inventory.notes')}
              value={formData.notes}
              onChangeText={(value) => setFormData(prev => ({ ...prev, notes: value }))}
              style={styles.dialogInput}
              mode="outlined"
              multiline
              numberOfLines={3}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={closeDialog}>{t('common.cancel')}</Button>
            <Button onPress={handleSave} mode="contained">
              {t('common.save')}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  listContent: {
    paddingBottom: 100,
  },
  movementCard: {
    marginBottom: spacing.sm,
    borderRadius: 12,
    elevation: 2,
  },
  movementHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  movementHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  movementInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  productNameRTL: {
    textAlign: 'right',
  },
  movementDate: {
    fontSize: 12,
    color: colors.onSurfaceVariant,
  },
  movementDateRTL: {
    textAlign: 'right',
  },
  typeChip: {
    marginLeft: spacing.sm,
  },
  movementDetails: {
    marginTop: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailRowRTL: {
    flexDirection: 'row-reverse',
  },
  detailText: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    marginLeft: spacing.sm,
    flex: 1,
  },
  detailTextRTL: {
    marginLeft: 0,
    marginRight: spacing.sm,
    textAlign: 'right',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
    marginBottom: spacing.xs,
  },
  emptyStateTextRTL: {
    textAlign: 'right',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
  emptyStateSubtextRTL: {
    textAlign: 'right',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  fabRTL: {
    right: undefined,
    left: 0,
  },
  menuButton: {
    marginBottom: spacing.sm,
    justifyContent: 'flex-start',
  },
  dialogInput: {
    marginBottom: spacing.sm,
  },
});

export default StockMovementScreen;
