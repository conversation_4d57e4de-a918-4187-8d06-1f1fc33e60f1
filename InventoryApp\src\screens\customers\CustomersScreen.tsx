import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  FAB,
  Searchbar,
  Chip,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchCustomers } from '../../store/slices/customerSlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const CustomersScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { customers, isLoading } = useSelector((state: RootState) => state.customers);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [searchQuery, setSearchQuery] = React.useState('');
  const [refreshing, setRefreshing] = React.useState(false);

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    try {
      await dispatch(fetchCustomers());
    } catch (error) {
      console.error('Failed to load customers:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCustomers();
    setRefreshing(false);
  };

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.phone?.includes(searchQuery)
  );

  const renderCustomerItem = ({ item }: { item: any }) => (
    <Card style={styles.customerCard} onPress={() => navigation.navigate('CustomerDetails', { customerId: item.id })}>
      <Card.Content>
        <View style={[styles.customerHeader, isRTL && styles.customerHeaderRTL]}>
          <View style={styles.customerInfo}>
            <Text style={[styles.customerName, isRTL && styles.customerNameRTL]}>
              {item.name}
            </Text>
            <Text style={[styles.customerType, isRTL && styles.customerTypeRTL]}>
              {item.type === 'business' ? t('customers.business') : t('customers.individual')}
            </Text>
          </View>
          <Chip
            mode="outlined"
            compact
            style={styles.statusChip}
          >
            {item.isActive ? t('common.active') : t('common.inactive')}
          </Chip>
        </View>
        
        <View style={styles.customerDetails}>
          {item.phone && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Icon name="phone" size={16} color={colors.onSurfaceVariant} />
              <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
                {item.phone}
              </Text>
            </View>
          )}
          {item.email && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Icon name="email" size={16} color={colors.onSurfaceVariant} />
              <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
                {item.email}
              </Text>
            </View>
          )}
          {item.address && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Icon name="location-on" size={16} color={colors.onSurfaceVariant} />
              <Text style={[styles.detailText, isRTL && styles.detailTextRTL]}>
                {item.address}
              </Text>
            </View>
          )}
        </View>
      </Card.Content>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="people" size={64} color={colors.onSurfaceVariant} />
      <Text style={[styles.emptyStateText, isRTL && styles.emptyStateTextRTL]}>
        {t('messages.noData')}
      </Text>
      <Text style={[styles.emptyStateSubtext, isRTL && styles.emptyStateSubtextRTL]}>
        {t('customers.addCustomer')}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title={t('customers.title')} />
        <Appbar.Action icon="filter-list" onPress={() => {}} />
      </Appbar.Header>

      <View style={styles.content}>
        <Searchbar
          placeholder={t('common.search')}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={[styles.searchbar, isRTL && styles.searchbarRTL]}
        />

        <FlatList
          data={filteredCustomers}
          renderItem={renderCustomerItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <FAB
        icon="plus"
        style={[styles.fab, isRTL && styles.fabRTL]}
        onPress={() => navigation.navigate('AddCustomer')}
        label={t('customers.addCustomer')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  searchbarRTL: {
    textAlign: 'right',
  },
  listContent: {
    paddingBottom: 100,
  },
  customerCard: {
    marginBottom: spacing.sm,
    borderRadius: 12,
    elevation: 2,
  },
  customerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  customerHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  customerInfo: {
    flex: 1,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  customerNameRTL: {
    textAlign: 'right',
  },
  customerType: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    textTransform: 'capitalize',
  },
  customerTypeRTL: {
    textAlign: 'right',
  },
  statusChip: {
    marginLeft: spacing.sm,
  },
  customerDetails: {
    marginTop: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailRowRTL: {
    flexDirection: 'row-reverse',
  },
  detailText: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    marginLeft: spacing.sm,
    flex: 1,
  },
  detailTextRTL: {
    marginLeft: 0,
    marginRight: spacing.sm,
    textAlign: 'right',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
    marginBottom: spacing.xs,
  },
  emptyStateTextRTL: {
    textAlign: 'right',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
  emptyStateSubtextRTL: {
    textAlign: 'right',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  fabRTL: {
    right: undefined,
    left: 0,
  },
});

export default CustomersScreen;
