import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Appbar,
  Card,
  Chip,
  Menu,
  Divider,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { createProduct, fetchCategories, fetchWarehouses } from '../../store/slices/inventorySlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface ProductFormData {
  name: string;
  sku: string;
  barcode: string;
  description: string;
  categoryId: string;
  unitPrice: string;
  costPrice: string;
  unit: string;
  minStock: string;
  maxStock: string;
  warehouseId: string;
  initialStock: string;
}

const AddProductScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { categories, warehouses, isLoading } = useSelector((state: RootState) => state.inventory);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    sku: '',
    barcode: '',
    description: '',
    categoryId: '',
    unitPrice: '',
    costPrice: '',
    unit: 'pcs',
    minStock: '10',
    maxStock: '100',
    warehouseId: '',
    initialStock: '0',
  });

  const [categoryMenuVisible, setCategoryMenuVisible] = useState(false);
  const [warehouseMenuVisible, setWarehouseMenuVisible] = useState(false);
  const [unitMenuVisible, setUnitMenuVisible] = useState(false);

  const units = ['pcs', 'kg', 'liter', 'meter', 'box', 'carton'];

  useEffect(() => {
    dispatch(fetchCategories());
    dispatch(fetchWarehouses());
  }, [dispatch]);

  const handleInputChange = (field: keyof ProductFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      Alert.alert(t('validation.error'), t('validation.productNameRequired'));
      return false;
    }
    if (!formData.sku.trim()) {
      Alert.alert(t('validation.error'), t('validation.skuRequired'));
      return false;
    }
    if (!formData.categoryId) {
      Alert.alert(t('validation.error'), t('validation.categoryRequired'));
      return false;
    }
    if (!formData.warehouseId) {
      Alert.alert(t('validation.error'), t('validation.warehouseRequired'));
      return false;
    }
    if (!formData.unitPrice || isNaN(parseFloat(formData.unitPrice))) {
      Alert.alert(t('validation.error'), t('validation.validPriceRequired'));
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      const productData = {
        name: formData.name.trim(),
        sku: formData.sku.trim(),
        barcode: formData.barcode.trim(),
        description: formData.description.trim(),
        categoryId: formData.categoryId,
        unitPrice: parseFloat(formData.unitPrice),
        costPrice: parseFloat(formData.costPrice) || 0,
        unit: formData.unit,
        minStock: parseInt(formData.minStock) || 0,
        maxStock: parseInt(formData.maxStock) || 0,
        warehouseId: formData.warehouseId,
        initialStock: parseInt(formData.initialStock) || 0,
      };

      await dispatch(createProduct(productData));
      Alert.alert(t('messages.success'), t('messages.productCreated'), [
        { text: t('common.ok'), onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert(t('messages.error'), t('messages.operationFailed'));
    }
  };

  const selectedCategory = categories.find(cat => cat.id === formData.categoryId);
  const selectedWarehouse = warehouses.find(wh => wh.id === formData.warehouseId);

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={t('inventory.addProduct')} />
        <Appbar.Action icon="check" onPress={handleSave} />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('inventory.basicInfo')}
            </Text>

            <TextInput
              label={t('inventory.productName')}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              style={styles.input}
              mode="outlined"
            />

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('inventory.sku')}
                value={formData.sku}
                onChangeText={(value) => handleInputChange('sku', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
              <TextInput
                label={t('inventory.barcode')}
                value={formData.barcode}
                onChangeText={(value) => handleInputChange('barcode', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
            </View>

            <TextInput
              label={t('inventory.description')}
              value={formData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              style={styles.input}
              mode="outlined"
              multiline
              numberOfLines={3}
            />
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('inventory.categorization')}
            </Text>

            <Menu
              visible={categoryMenuVisible}
              onDismiss={() => setCategoryMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setCategoryMenuVisible(true)}
                  style={styles.menuButton}
                  contentStyle={[styles.menuButtonContent, isRTL && styles.menuButtonContentRTL]}
                >
                  {selectedCategory ? selectedCategory.name : t('inventory.selectCategory')}
                </Button>
              }
            >
              {categories.map((category) => (
                <Menu.Item
                  key={category.id}
                  onPress={() => {
                    handleInputChange('categoryId', category.id);
                    setCategoryMenuVisible(false);
                  }}
                  title={category.name}
                />
              ))}
            </Menu>

            <Menu
              visible={warehouseMenuVisible}
              onDismiss={() => setWarehouseMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setWarehouseMenuVisible(true)}
                  style={styles.menuButton}
                  contentStyle={[styles.menuButtonContent, isRTL && styles.menuButtonContentRTL]}
                >
                  {selectedWarehouse ? selectedWarehouse.name : t('inventory.selectWarehouse')}
                </Button>
              }
            >
              {warehouses.map((warehouse) => (
                <Menu.Item
                  key={warehouse.id}
                  onPress={() => {
                    handleInputChange('warehouseId', warehouse.id);
                    setWarehouseMenuVisible(false);
                  }}
                  title={warehouse.name}
                />
              ))}
            </Menu>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('inventory.pricing')}
            </Text>

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('inventory.unitPrice')}
                value={formData.unitPrice}
                onChangeText={(value) => handleInputChange('unitPrice', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="numeric"
              />
              <TextInput
                label={t('inventory.costPrice')}
                value={formData.costPrice}
                onChangeText={(value) => handleInputChange('costPrice', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="numeric"
              />
            </View>

            <Menu
              visible={unitMenuVisible}
              onDismiss={() => setUnitMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setUnitMenuVisible(true)}
                  style={styles.menuButton}
                  contentStyle={[styles.menuButtonContent, isRTL && styles.menuButtonContentRTL]}
                >
                  {formData.unit}
                </Button>
              }
            >
              {units.map((unit) => (
                <Menu.Item
                  key={unit}
                  onPress={() => {
                    handleInputChange('unit', unit);
                    setUnitMenuVisible(false);
                  }}
                  title={unit}
                />
              ))}
            </Menu>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('inventory.stockSettings')}
            </Text>

            <View style={[styles.row, isRTL && styles.rowRTL]}>
              <TextInput
                label={t('inventory.minStock')}
                value={formData.minStock}
                onChangeText={(value) => handleInputChange('minStock', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="numeric"
              />
              <TextInput
                label={t('inventory.maxStock')}
                value={formData.maxStock}
                onChangeText={(value) => handleInputChange('maxStock', value)}
                style={[styles.input, styles.halfInput]}
                mode="outlined"
                keyboardType="numeric"
              />
            </View>

            <TextInput
              label={t('inventory.initialStock')}
              value={formData.initialStock}
              onChangeText={(value) => handleInputChange('initialStock', value)}
              style={styles.input}
              mode="outlined"
              keyboardType="numeric"
            />
          </Card.Content>
        </Card>

        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.saveButton}
          loading={isLoading}
          disabled={isLoading}
        >
          {t('common.save')}
        </Button>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  card: {
    marginBottom: spacing.md,
    borderRadius: 12,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
  },
  sectionTitleRTL: {
    textAlign: 'right',
  },
  input: {
    marginBottom: spacing.sm,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rowRTL: {
    flexDirection: 'row-reverse',
  },
  halfInput: {
    width: '48%',
  },
  menuButton: {
    marginBottom: spacing.sm,
    justifyContent: 'flex-start',
  },
  menuButtonContent: {
    justifyContent: 'flex-start',
  },
  menuButtonContentRTL: {
    justifyContent: 'flex-end',
  },
  saveButton: {
    marginVertical: spacing.lg,
    borderRadius: 8,
  },
});

export default AddProductScreen;
