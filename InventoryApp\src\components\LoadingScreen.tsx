import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ActivityIndicator, Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { commonStyles, colors } from '../theme';

const LoadingScreen: React.FC = () => {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color={colors.primary} />
      <Text style={styles.text}>{t('common.loading')}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...commonStyles.container,
    ...commonStyles.center,
  },
  text: {
    marginTop: 16,
    fontSize: 16,
    color: colors.onSurface,
  },
});

export default LoadingScreen;
