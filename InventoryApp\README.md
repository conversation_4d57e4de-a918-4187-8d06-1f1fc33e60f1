# Inventory Management Mobile App

A professional cross-platform mobile application for comprehensive inventory and customer management with full Arabic RTL support.

## 🚀 Features

### ✅ Completed Features
- **Cross-platform Mobile App** - React Native with Expo for iOS and Android
- **TypeScript Support** - Full type safety and better development experience
- **Authentication System** - Secure login/logout with user management
- **Multi-language Support** - English and Arabic with RTL layout support
- **Professional UI/UX** - Material Design with React Native Paper
- **State Management** - Redux Toolkit for scalable application state
- **Local Database** - SQLite for offline-first data storage
- **Navigation System** - Stack and tab navigation with RTL support
- **Theme System** - Light/dark theme support with customizable colors

### 🔄 Core Modules (Foundation Ready)
- **Dashboard** - Overview with statistics and quick actions
- **Inventory Management** - Product tracking with stock levels
- **Customer Management** - Customer database and profiles
- **Invoice System** - Invoice generation and management
- **Settings** - App configuration and user preferences

### 🎯 Planned Features
- **Inventory Tracking** - Warehouses, stock movements, carton/piece tracking
- **Invoice Generation** - PDF export with professional formatting
- **Purchase Orders** - Order creation and tracking
- **Financial Reports** - Accounting and cumulative tracking
- **WhatsApp Integration** - Document sharing via WhatsApp
- **PDF Generation** - Professional document export
- **Barcode Scanning** - Product identification and tracking

## 🛠 Technology Stack

- **Framework**: React Native with Expo SDK
- **Language**: TypeScript
- **UI Library**: React Native Paper (Material Design)
- **State Management**: Redux Toolkit
- **Database**: SQLite (expo-sqlite)
- **Navigation**: React Navigation v7
- **Internationalization**: i18next
- **Icons**: React Native Vector Icons
- **Storage**: AsyncStorage

## 📱 Screenshots

The app includes:
- Professional login screen with demo credentials
- Multi-language toggle (English/Arabic)
- RTL-aware layouts for Arabic users
- Material Design components
- Bottom tab navigation
- Comprehensive dashboard
- Empty state screens for all modules

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Expo Go app on your mobile device

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd InventoryApp
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npx expo start
   ```

4. **Run on device**
   - Scan the QR code with Expo Go app
   - Or press 'a' for Android emulator
   - Or press 'i' for iOS simulator

### Demo Credentials
- **Username**: admin
- **Password**: admin123

## 📁 Project Structure

```
InventoryApp/
├── src/
│   ├── components/          # Reusable UI components
│   ├── database/           # SQLite database setup and services
│   ├── i18n/              # Internationalization setup
│   │   └── locales/       # Translation files (en.json, ar.json)
│   ├── navigation/        # Navigation configuration
│   ├── screens/           # Screen components
│   │   ├── auth/          # Authentication screens
│   │   ├── dashboard/     # Dashboard screens
│   │   ├── inventory/     # Inventory management screens
│   │   ├── customers/     # Customer management screens
│   │   ├── invoices/      # Invoice screens
│   │   └── settings/      # Settings screens
│   ├── store/             # Redux store and slices
│   ├── theme/             # Theme configuration and styles
│   └── types/             # TypeScript type definitions
├── App.tsx                # Main app component
└── package.json
```

## 🗄 Database Schema

The app uses SQLite with the following main tables:
- **users** - User authentication and profiles
- **warehouses** - Storage locations
- **categories** - Product categorization
- **products** - Product information
- **stock** - Inventory levels and tracking
- **customers** - Customer database
- **suppliers** - Supplier information
- **invoices** - Invoice records
- **invoice_items** - Invoice line items
- **purchase_orders** - Purchase order tracking
- **transactions** - Financial transactions
- **stock_movements** - Inventory movement history

## 🌐 Internationalization

The app supports:
- **English** (default)
- **Arabic** with full RTL support

Language files are located in `src/i18n/locales/`:
- `en.json` - English translations
- `ar.json` - Arabic translations

## 🎨 Theming

The app includes:
- **Light Theme** (default)
- **Dark Theme** support
- **RTL-aware styling**
- **Material Design 3** color system
- **Customizable color palette**

## 📊 State Management

Redux Toolkit slices:
- **authSlice** - Authentication state
- **appSlice** - App settings (language, theme, etc.)
- **inventorySlice** - Inventory management
- **customerSlice** - Customer management
- **invoiceSlice** - Invoice management

## 🔧 Development

### Adding New Features
1. Create types in `src/types/index.ts`
2. Add database schema in `src/database/schema.ts`
3. Implement database service methods
4. Create Redux slice for state management
5. Build UI screens and components
6. Add navigation routes
7. Update translations

### Code Style
- TypeScript for type safety
- Functional components with hooks
- Redux Toolkit for state management
- Material Design principles
- RTL-aware styling

## 📝 Next Steps

1. **Inventory Module Enhancement**
   - Complete product CRUD operations
   - Implement stock movements
   - Add barcode scanning

2. **Invoice System**
   - PDF generation
   - Professional invoice templates
   - Email/WhatsApp sharing

3. **Advanced Features**
   - Purchase order system
   - Financial reporting
   - Data export/import
   - Backup and restore

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team

---

**Built with ❤️ using React Native and Expo**
