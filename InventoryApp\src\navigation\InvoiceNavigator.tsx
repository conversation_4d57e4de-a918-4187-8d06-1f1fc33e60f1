import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// Import screens
import InvoicesScreen from '../screens/invoices/InvoicesScreen';
import CreateInvoiceScreen from '../screens/invoices/CreateInvoiceScreen';
import InvoiceDetailsScreen from '../screens/invoices/InvoiceDetailsScreen';

export type InvoiceStackParamList = {
  InvoicesList: undefined;
  CreateInvoice: { customerId?: string };
  EditInvoice: { invoiceId: string };
  InvoiceDetails: { invoiceId: string };
};

const Stack = createStackNavigator<InvoiceStackParamList>();

const InvoiceNavigator: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Stack.Navigator
      initialRouteName="InvoicesList"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen 
        name="InvoicesList" 
        component={InvoicesScreen}
        options={{
          title: t('navigation.invoices'),
        }}
      />
      <Stack.Screen 
        name="CreateInvoice" 
        component={CreateInvoiceScreen}
        options={{
          title: t('invoices.createInvoice'),
        }}
      />
      <Stack.Screen 
        name="InvoiceDetails" 
        component={InvoiceDetailsScreen}
        options={{
          title: t('invoices.invoiceDetails'),
        }}
      />
    </Stack.Navigator>
  );
};

export default InvoiceNavigator;
