import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Snackbar,
  Appbar,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootState, AppDispatch } from '../../store';
import { registerUser, clearError } from '../../store/slices/authSlice';
import { AuthStackParamList } from '../../navigation/AuthNavigator';
import { commonStyles, colors, spacing } from '../../theme';

type RegisterScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'Register'>;

interface Props {
  navigation: RegisterScreenNavigationProp;
}

const RegisterScreen: React.FC<Props> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error } = useSelector((state: RootState) => state.auth);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleRegister = async () => {
    if (!formData.username.trim() || !formData.email.trim() || !formData.password.trim()) {
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      // Handle password mismatch
      return;
    }

    try {
      await dispatch(registerUser({
        username: formData.username.trim(),
        email: formData.email.trim(),
        password: formData.password,
        role: 'employee',
      })).unwrap();
    } catch (error) {
      // Error is handled by the slice
    }
  };

  const goBack = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={goBack} />
        <Appbar.Content title={t('auth.register')} />
      </Appbar.Header>

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <Card style={styles.card}>
            <Card.Content>
              <Text style={[styles.cardTitle, isRTL && styles.cardTitleRTL]}>
                {t('auth.createAccount')}
              </Text>

              <TextInput
                label={t('auth.username')}
                value={formData.username}
                onChangeText={(value) => handleInputChange('username', value)}
                mode="outlined"
                style={[styles.input, isRTL && styles.inputRTL]}
                left={<TextInput.Icon icon="account" />}
                autoCapitalize="none"
                autoCorrect={false}
                textAlign={isRTL ? 'right' : 'left'}
              />

              <TextInput
                label={t('auth.email')}
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                mode="outlined"
                style={[styles.input, isRTL && styles.inputRTL]}
                left={<TextInput.Icon icon="email" />}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                textAlign={isRTL ? 'right' : 'left'}
              />

              <TextInput
                label={t('auth.password')}
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                mode="outlined"
                style={[styles.input, isRTL && styles.inputRTL]}
                left={<TextInput.Icon icon="lock" />}
                right={
                  <TextInput.Icon
                    icon={showPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowPassword(!showPassword)}
                  />
                }
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                textAlign={isRTL ? 'right' : 'left'}
              />

              <TextInput
                label="Confirm Password"
                value={formData.confirmPassword}
                onChangeText={(value) => handleInputChange('confirmPassword', value)}
                mode="outlined"
                style={[styles.input, isRTL && styles.inputRTL]}
                left={<TextInput.Icon icon="lock-check" />}
                right={
                  <TextInput.Icon
                    icon={showConfirmPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  />
                }
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
                autoCorrect={false}
                textAlign={isRTL ? 'right' : 'left'}
              />

              <Button
                mode="contained"
                onPress={handleRegister}
                loading={isLoading}
                disabled={
                  isLoading ||
                  !formData.username.trim() ||
                  !formData.email.trim() ||
                  !formData.password.trim() ||
                  formData.password !== formData.confirmPassword
                }
                style={styles.registerButton}
                contentStyle={styles.registerButtonContent}
              >
                {t('auth.register')}
              </Button>

              <View style={styles.loginContainer}>
                <Text style={[styles.loginText, isRTL && styles.loginTextRTL]}>
                  {t('auth.alreadyHaveAccount')}
                </Text>
                <Button
                  mode="text"
                  onPress={goBack}
                  style={styles.loginButton}
                >
                  {t('auth.login')}
                </Button>
              </View>
            </Card.Content>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>

      <Snackbar
        visible={!!error}
        onDismiss={() => dispatch(clearError())}
        duration={4000}
        action={{
          label: t('common.ok'),
          onPress: () => dispatch(clearError()),
        }}
      >
        {error}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: spacing.md,
    justifyContent: 'center',
  },
  card: {
    borderRadius: 16,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  cardTitleRTL: {
    textAlign: 'right',
  },
  input: {
    marginBottom: spacing.md,
  },
  inputRTL: {
    textAlign: 'right',
  },
  registerButton: {
    marginTop: spacing.md,
    marginBottom: spacing.md,
    borderRadius: 8,
  },
  registerButtonContent: {
    paddingVertical: spacing.xs,
  },
  loginContainer: {
    alignItems: 'center',
    marginTop: spacing.md,
  },
  loginText: {
    color: colors.onSurfaceVariant,
    marginBottom: spacing.xs,
  },
  loginTextRTL: {
    textAlign: 'right',
  },
  loginButton: {
    marginTop: spacing.xs,
  },
});

export default RegisterScreen;
