import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import WhatsAppButton from '../../src/components/WhatsAppButton';
import WhatsAppService from '../../src/services/WhatsAppService';
import appSlice from '../../src/store/slices/appSlice';

// Mock WhatsApp Service
jest.mock('../../src/services/WhatsAppService');

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock Alert
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
}));

const mockStore = configureStore({
  reducer: {
    app: appSlice,
  },
  preloadedState: {
    app: {
      isRTL: false,
      language: 'en',
      theme: 'light',
    },
  },
});

const MockedWhatsAppService = WhatsAppService as jest.MockedClass<typeof WhatsAppService>;

describe('WhatsAppButton', () => {
  const mockContact = {
    name: 'John Doe',
    phone: '**********',
    email: '<EMAIL>',
  };

  const mockWhatsAppService = {
    sendMessage: jest.fn(),
    sendInvoice: jest.fn(),
    sendPurchaseOrder: jest.fn(),
    sendCustomerInfo: jest.fn(),
    sendStockAlert: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    MockedWhatsAppService.getInstance.mockReturnValue(mockWhatsAppService as any);
  });

  const renderWithProvider = (component: React.ReactElement) => {
    return render(
      <Provider store={mockStore}>
        {component}
      </Provider>
    );
  };

  describe('Basic Rendering', () => {
    test('should render button with default props', () => {
      const { getByText } = renderWithProvider(
        <WhatsAppButton contact={mockContact} />
      );

      expect(getByText('whatsapp.sendMessage')).toBeTruthy();
    });

    test('should render button with custom type', () => {
      const { getByText } = renderWithProvider(
        <WhatsAppButton contact={mockContact} type="invoice" />
      );

      expect(getByText('whatsapp.sendInvoice')).toBeTruthy();
    });

    test('should render icon-only button', () => {
      const { queryByText } = renderWithProvider(
        <WhatsAppButton contact={mockContact} iconOnly />
      );

      // Should not show text when iconOnly is true
      expect(queryByText('whatsapp.sendMessage')).toBeFalsy();
    });
  });

  describe('Message Sending', () => {
    test('should send basic message', async () => {
      mockWhatsAppService.sendMessage.mockResolvedValueOnce(true);

      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          data={{ message: 'Hello World' }}
        />
      );

      fireEvent.press(getByText('whatsapp.sendMessage'));

      await waitFor(() => {
        expect(mockWhatsAppService.sendMessage).toHaveBeenCalledWith(
          '**********',
          'Hello World'
        );
      });
    });

    test('should send invoice', async () => {
      mockWhatsAppService.sendInvoice.mockResolvedValueOnce(true);

      const mockInvoice = {
        invoiceNumber: 'INV-001',
        total: 1000,
        status: 'paid',
      };

      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          type="invoice"
          data={mockInvoice}
        />
      );

      fireEvent.press(getByText('whatsapp.sendInvoice'));

      await waitFor(() => {
        expect(mockWhatsAppService.sendInvoice).toHaveBeenCalledWith(
          mockContact,
          mockInvoice,
          undefined
        );
      });
    });

    test('should send invoice with document', async () => {
      mockWhatsAppService.sendInvoice.mockResolvedValueOnce(true);

      const mockInvoice = {
        invoiceNumber: 'INV-001',
        total: 1000,
        status: 'paid',
      };

      const documentUri = 'file://invoice.pdf';

      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          type="invoice"
          data={mockInvoice}
          documentUri={documentUri}
        />
      );

      fireEvent.press(getByText('whatsapp.sendInvoice'));

      await waitFor(() => {
        expect(mockWhatsAppService.sendInvoice).toHaveBeenCalledWith(
          mockContact,
          mockInvoice,
          documentUri
        );
      });
    });

    test('should send purchase order', async () => {
      mockWhatsAppService.sendPurchaseOrder.mockResolvedValueOnce(true);

      const mockPO = {
        orderNumber: 'PO-001',
        totalAmount: 2000,
        status: 'approved',
      };

      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          type="purchaseOrder"
          data={mockPO}
        />
      );

      fireEvent.press(getByText('whatsapp.sendPurchaseOrder'));

      await waitFor(() => {
        expect(mockWhatsAppService.sendPurchaseOrder).toHaveBeenCalledWith(
          mockContact,
          mockPO,
          undefined
        );
      });
    });

    test('should send customer info', async () => {
      mockWhatsAppService.sendCustomerInfo.mockResolvedValueOnce(true);

      const mockCustomer = {
        name: 'Jane Doe',
        email: '<EMAIL>',
        phone: '+**********',
      };

      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          type="customerInfo"
          data={mockCustomer}
        />
      );

      fireEvent.press(getByText('whatsapp.sendCustomerInfo'));

      await waitFor(() => {
        expect(mockWhatsAppService.sendCustomerInfo).toHaveBeenCalledWith(
          mockContact,
          mockCustomer
        );
      });
    });

    test('should send stock alert', async () => {
      mockWhatsAppService.sendStockAlert.mockResolvedValueOnce(true);

      const mockProducts = [
        { name: 'Product A', currentStock: 5, minStock: 10 },
        { name: 'Product B', currentStock: 2, minStock: 15 },
      ];

      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          type="stockAlert"
          data={mockProducts}
        />
      );

      fireEvent.press(getByText('whatsapp.sendStockAlert'));

      await waitFor(() => {
        expect(mockWhatsAppService.sendStockAlert).toHaveBeenCalledWith(
          mockContact,
          mockProducts
        );
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle missing phone number', async () => {
      const { Alert } = require('react-native');
      
      const contactWithoutPhone = {
        name: 'John Doe',
        phone: '',
      };

      const { getByText } = renderWithProvider(
        <WhatsAppButton contact={contactWithoutPhone} />
      );

      fireEvent.press(getByText('whatsapp.sendMessage'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'messages.error',
          'messages.phoneNumberRequired'
        );
      });
    });

    test('should handle missing invoice data', async () => {
      const { Alert } = require('react-native');

      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          type="invoice"
          // No data provided
        />
      );

      fireEvent.press(getByText('whatsapp.sendInvoice'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'messages.error',
          'messages.invoiceDataRequired'
        );
      });
    });

    test('should handle service errors', async () => {
      const { Alert } = require('react-native');
      mockWhatsAppService.sendMessage.mockRejectedValueOnce(new Error('Service error'));

      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          data={{ message: 'Hello World' }}
        />
      );

      fireEvent.press(getByText('whatsapp.sendMessage'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'messages.error',
          'messages.whatsappSendFailed'
        );
      });
    });

    test('should handle unsupported message type', async () => {
      const { Alert } = require('react-native');

      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          type="unsupported" as any
        />
      );

      fireEvent.press(getByText('whatsapp.sendMessage'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'messages.error',
          'messages.unsupportedMessageType'
        );
      });
    });
  });

  describe('Loading States', () => {
    test('should show loading state during send', async () => {
      let resolvePromise: (value: boolean) => void;
      const promise = new Promise<boolean>((resolve) => {
        resolvePromise = resolve;
      });
      
      mockWhatsAppService.sendMessage.mockReturnValueOnce(promise);

      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          data={{ message: 'Hello World' }}
        />
      );

      const button = getByText('whatsapp.sendMessage');
      fireEvent.press(button);

      // Button should be disabled during loading
      expect(button.props.accessibilityState?.disabled).toBe(true);

      // Resolve the promise
      resolvePromise!(true);

      await waitFor(() => {
        expect(button.props.accessibilityState?.disabled).toBe(false);
      });
    });
  });

  describe('RTL Support', () => {
    test('should apply RTL styles when isRTL is true', () => {
      const rtlStore = configureStore({
        reducer: {
          app: appSlice,
        },
        preloadedState: {
          app: {
            isRTL: true,
            language: 'ar',
            theme: 'light',
          },
        },
      });

      const { getByText } = render(
        <Provider store={rtlStore}>
          <WhatsAppButton contact={mockContact} />
        </Provider>
      );

      const button = getByText('whatsapp.sendMessage');
      expect(button).toBeTruthy();
      // RTL styles should be applied (this would need more specific testing based on implementation)
    });
  });

  describe('Different Button Modes', () => {
    test('should render contained button', () => {
      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          mode="contained"
        />
      );

      const button = getByText('whatsapp.sendMessage');
      expect(button).toBeTruthy();
    });

    test('should render outlined button', () => {
      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          mode="outlined"
        />
      );

      const button = getByText('whatsapp.sendMessage');
      expect(button).toBeTruthy();
    });

    test('should render text button', () => {
      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          mode="text"
        />
      );

      const button = getByText('whatsapp.sendMessage');
      expect(button).toBeTruthy();
    });

    test('should render compact button', () => {
      const { getByText } = renderWithProvider(
        <WhatsAppButton 
          contact={mockContact} 
          compact
        />
      );

      const button = getByText('whatsapp.sendMessage');
      expect(button).toBeTruthy();
    });
  });
});
