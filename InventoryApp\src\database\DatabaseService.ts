import * as SQLite from 'expo-sqlite';
import { createTables, insertSampleData, DATABASE_NAME } from './schema';

class DatabaseService {
  private db: SQLite.SQLiteDatabase | null = null;

  async initialize(): Promise<void> {
    try {
      this.db = await SQLite.openDatabaseAsync(DATABASE_NAME);
      await createTables(this.db);
      await insertSampleData(this.db);

      // Add the specific user
      await this.addSpecificUser();

      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  getDatabase(): SQLite.SQLiteDatabase {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  async executeQuery(query: string, params: any[] = []): Promise<any> {
    const db = this.getDatabase();
    try {
      const result = await db.getAllAsync(query, params);
      return result;
    } catch (error) {
      console.error('Query execution failed:', error);
      throw error;
    }
  }

  async executeUpdate(query: string, params: any[] = []): Promise<SQLite.SQLiteRunResult> {
    const db = this.getDatabase();
    try {
      const result = await db.runAsync(query, params);
      return result;
    } catch (error) {
      console.error('Update execution failed:', error);
      throw error;
    }
  }

  // User operations
  async createUser(user: any): Promise<string> {
    const id = `user-${Date.now()}`;
    const query = `
      INSERT INTO users (id, username, email, password_hash, role, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `;
    await this.executeUpdate(query, [id, user.username, user.email, user.passwordHash, user.role]);
    return id;
  }

  async getUserByUsername(username: string): Promise<any> {
    const query = 'SELECT * FROM users WHERE username = ?';
    const result = await this.executeQuery(query, [username]);
    return result[0] || null;
  }

  // Add specific user with provided details
  async addSpecificUser(): Promise<void> {
    try {
      // Check if user already exists
      const existingUser = await this.getUserByUsername('lolo9090');

      if (!existingUser) {
        await this.createUser({
          username: 'lolo9090',
          email: '<EMAIL>',
          passwordHash: 'koko2030', // In production, this should be hashed
          role: 'admin'
        });
        console.log('User lolo9090 added successfully');
      } else {
        console.log('User lolo9090 already exists');
      }
    } catch (error) {
      console.error('Error adding user:', error);
    }
  }

  // Warehouse operations
  async getWarehouses(): Promise<any[]> {
    const query = 'SELECT * FROM warehouses WHERE is_active = 1 ORDER BY name';
    return await this.executeQuery(query);
  }

  async createWarehouse(warehouse: any): Promise<string> {
    const id = `warehouse-${Date.now()}`;
    const query = `
      INSERT INTO warehouses (id, name, location, description, created_at, updated_at)
      VALUES (?, ?, ?, ?, datetime('now'), datetime('now'))
    `;
    await this.executeUpdate(query, [id, warehouse.name, warehouse.location, warehouse.description]);
    return id;
  }

  // Category operations
  async getCategories(): Promise<any[]> {
    const query = 'SELECT * FROM categories WHERE is_active = 1 ORDER BY name';
    return await this.executeQuery(query);
  }

  async createCategory(category: any): Promise<string> {
    const id = `cat-${Date.now()}`;
    const query = `
      INSERT INTO categories (id, name, name_ar, description, description_ar, parent_id, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `;
    await this.executeUpdate(query, [
      id, category.name, category.nameAr, category.description, 
      category.descriptionAr, category.parentId
    ]);
    return id;
  }

  // Product operations
  async getProducts(): Promise<any[]> {
    const query = `
      SELECT p.*, c.name as category_name, c.name_ar as category_name_ar
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_active = 1
      ORDER BY p.name
    `;
    return await this.executeQuery(query);
  }

  async createProduct(product: any): Promise<string> {
    const id = `prod-${Date.now()}`;
    const query = `
      INSERT INTO products (
        id, name, name_ar, description, description_ar, sku, barcode,
        category_id, unit_price, cost_price, min_stock_level, max_stock_level,
        unit, image_url, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `;
    await this.executeUpdate(query, [
      id, product.name, product.nameAr, product.description, product.descriptionAr,
      product.sku, product.barcode, product.categoryId, product.unitPrice,
      product.costPrice, product.minStockLevel, product.maxStockLevel,
      product.unit, product.imageUrl
    ]);
    return id;
  }

  async updateProduct(id: string, product: any): Promise<void> {
    const query = `
      UPDATE products SET
        name = ?, name_ar = ?, description = ?, description_ar = ?,
        sku = ?, barcode = ?, category_id = ?, unit_price = ?,
        cost_price = ?, min_stock_level = ?, max_stock_level = ?,
        unit = ?, image_url = ?, updated_at = datetime('now')
      WHERE id = ?
    `;
    await this.executeUpdate(query, [
      product.name, product.nameAr, product.description, product.descriptionAr,
      product.sku, product.barcode, product.categoryId, product.unitPrice,
      product.costPrice, product.minStockLevel, product.maxStockLevel,
      product.unit, product.imageUrl, id
    ]);
  }

  // Stock operations
  async getStock(warehouseId?: string): Promise<any[]> {
    let query = `
      SELECT s.*, p.name, p.name_ar, p.sku, p.unit, w.name as warehouse_name
      FROM stock s
      JOIN products p ON s.product_id = p.id
      JOIN warehouses w ON s.warehouse_id = w.id
    `;
    const params: any[] = [];
    
    if (warehouseId) {
      query += ' WHERE s.warehouse_id = ?';
      params.push(warehouseId);
    }
    
    query += ' ORDER BY p.name';
    return await this.executeQuery(query, params);
  }

  async updateStock(productId: string, warehouseId: string, quantity: number, cartonQuantity: number = 0): Promise<void> {
    const query = `
      INSERT OR REPLACE INTO stock (
        id, product_id, warehouse_id, quantity, carton_quantity, last_updated
      ) VALUES (
        COALESCE((SELECT id FROM stock WHERE product_id = ? AND warehouse_id = ?), ?),
        ?, ?, ?, ?, datetime('now')
      )
    `;
    const stockId = `stock-${productId}-${warehouseId}`;
    await this.executeUpdate(query, [productId, warehouseId, stockId, productId, warehouseId, quantity, cartonQuantity]);
  }

  // Customer operations
  async getCustomers(): Promise<any[]> {
    const query = 'SELECT * FROM customers WHERE is_active = 1 ORDER BY name';
    return await this.executeQuery(query);
  }

  async createCustomer(customer: any): Promise<string> {
    const id = `cust-${Date.now()}`;
    const query = `
      INSERT INTO customers (
        id, name, name_ar, email, phone, whatsapp, address, address_ar,
        city, city_ar, country, country_ar, customer_type, tax_number,
        credit_limit, payment_terms, notes, notes_ar, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `;
    await this.executeUpdate(query, [
      id, customer.name, customer.nameAr, customer.email, customer.phone,
      customer.whatsapp, customer.address, customer.addressAr, customer.city,
      customer.cityAr, customer.country, customer.countryAr, customer.customerType,
      customer.taxNumber, customer.creditLimit, customer.paymentTerms,
      customer.notes, customer.notesAr
    ]);
    return id;
  }

  // Invoice operations
  async getInvoices(): Promise<any[]> {
    const query = `
      SELECT i.*, c.name as customer_name, c.name_ar as customer_name_ar, w.name as warehouse_name
      FROM invoices i
      JOIN customers c ON i.customer_id = c.id
      JOIN warehouses w ON i.warehouse_id = w.id
      ORDER BY i.invoice_date DESC
    `;
    return await this.executeQuery(query);
  }

  async createInvoice(invoice: any): Promise<string> {
    const id = `inv-${Date.now()}`;
    const invoiceNumber = `INV-${Date.now()}`;
    const query = `
      INSERT INTO invoices (
        id, invoice_number, customer_id, warehouse_id, invoice_date, due_date,
        subtotal, tax_amount, discount_amount, total_amount, status, payment_status,
        notes, notes_ar, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `;
    await this.executeUpdate(query, [
      id, invoiceNumber, invoice.customerId, invoice.warehouseId, invoice.invoiceDate,
      invoice.dueDate, invoice.subtotal, invoice.taxAmount, invoice.discountAmount,
      invoice.totalAmount, invoice.status, invoice.paymentStatus, invoice.notes, invoice.notesAr
    ]);
    return id;
  }

  // Purchase Order operations
  async getPurchaseOrders(): Promise<any[]> {
    const query = `
      SELECT po.*, s.name as supplier_name, s.name_ar as supplier_name_ar, w.name as warehouse_name
      FROM purchase_orders po
      JOIN suppliers s ON po.supplier_id = s.id
      JOIN warehouses w ON po.warehouse_id = w.id
      ORDER BY po.order_date DESC
    `;
    return await this.executeQuery(query);
  }

  // Transaction operations
  async getTransactions(startDate?: string, endDate?: string): Promise<any[]> {
    let query = 'SELECT * FROM transactions';
    const params: any[] = [];
    
    if (startDate && endDate) {
      query += ' WHERE date BETWEEN ? AND ?';
      params.push(startDate, endDate);
    }
    
    query += ' ORDER BY date DESC';
    return await this.executeQuery(query, params);
  }

  async createTransaction(transaction: any): Promise<string> {
    const id = `trans-${Date.now()}`;
    const query = `
      INSERT INTO transactions (id, type, reference_id, amount, description, description_ar, date, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `;
    await this.executeUpdate(query, [
      id, transaction.type, transaction.referenceId, transaction.amount,
      transaction.description, transaction.descriptionAr, transaction.date
    ]);
    return id;
  }

  // Purchase Order operations
  async getAllPurchaseOrders(): Promise<any[]> {
    const query = `
      SELECT po.*, s.name as supplier_name
      FROM purchase_orders po
      LEFT JOIN suppliers s ON po.supplier_id = s.id
      ORDER BY po.created_at DESC
    `;
    return await this.executeQuery(query);
  }

  async getPurchaseOrderById(id: string): Promise<any> {
    const query = `
      SELECT po.*, s.name as supplier_name, s.email as supplier_email, s.phone as supplier_phone
      FROM purchase_orders po
      LEFT JOIN suppliers s ON po.supplier_id = s.id
      WHERE po.id = ?
    `;
    const result = await this.executeQuery(query, [id]);
    if (result.length === 0) return null;

    const order = result[0];

    // Get order items
    const itemsQuery = `
      SELECT poi.*, p.name as product_name
      FROM purchase_order_items poi
      LEFT JOIN products p ON poi.product_id = p.id
      WHERE poi.purchase_order_id = ?
    `;
    const items = await this.executeQuery(itemsQuery, [id]);
    order.items = items;

    return order;
  }

  async createPurchaseOrder(orderData: any): Promise<string> {
    const id = `po-${Date.now()}`;

    // Create purchase order
    const orderQuery = `
      INSERT INTO purchase_orders (
        id, po_number, supplier_id, warehouse_id, order_date, expected_date,
        subtotal, tax_amount, total_amount, status, notes, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `;

    await this.executeUpdate(orderQuery, [
      id,
      orderData.orderNumber,
      orderData.supplierId || 'default-supplier',
      orderData.warehouseId || 'default-warehouse',
      orderData.orderDate,
      orderData.expectedDelivery,
      orderData.subtotal,
      orderData.taxAmount,
      orderData.totalAmount,
      orderData.status,
      orderData.notes
    ]);

    // Create order items
    if (orderData.items && orderData.items.length > 0) {
      for (const item of orderData.items) {
        const itemId = `poi-${Date.now()}-${Math.random()}`;
        const itemQuery = `
          INSERT INTO purchase_order_items (
            id, purchase_order_id, product_id, quantity, unit_price, total_amount
          ) VALUES (?, ?, ?, ?, ?, ?)
        `;

        await this.executeUpdate(itemQuery, [
          itemId,
          id,
          item.productId,
          item.quantity,
          item.unitCost,
          item.total
        ]);
      }
    }

    return id;
  }

  async updatePurchaseOrderStatus(id: string, status: string): Promise<void> {
    const query = `
      UPDATE purchase_orders
      SET status = ?, updated_at = datetime('now')
      WHERE id = ?
    `;
    await this.executeUpdate(query, [status, id]);
  }

  async deletePurchaseOrder(id: string): Promise<void> {
    // Delete order items first
    await this.executeUpdate('DELETE FROM purchase_order_items WHERE purchase_order_id = ?', [id]);

    // Delete purchase order
    await this.executeUpdate('DELETE FROM purchase_orders WHERE id = ?', [id]);
  }

  // Financial operations
  async getRecentTransactions(limit: number = 10): Promise<any[]> {
    const query = `
      SELECT * FROM transactions
      ORDER BY created_at DESC
      LIMIT ?
    `;
    return await this.executeQuery(query, [limit]);
  }

  async getFinancialSummary(period: string): Promise<any> {
    let dateFilter = '';
    const now = new Date();

    switch (period) {
      case 'week':
        const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
        dateFilter = `WHERE date >= '${weekStart.toISOString().split('T')[0]}'`;
        break;
      case 'month':
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        dateFilter = `WHERE date >= '${monthStart.toISOString().split('T')[0]}'`;
        break;
      case 'quarter':
        const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        dateFilter = `WHERE date >= '${quarterStart.toISOString().split('T')[0]}'`;
        break;
      case 'year':
        const yearStart = new Date(now.getFullYear(), 0, 1);
        dateFilter = `WHERE date >= '${yearStart.toISOString().split('T')[0]}'`;
        break;
    }

    const revenueQuery = `
      SELECT COALESCE(SUM(amount), 0) as total
      FROM transactions
      ${dateFilter} AND type IN ('income', 'sale')
    `;

    const expenseQuery = `
      SELECT COALESCE(SUM(amount), 0) as total
      FROM transactions
      ${dateFilter} AND type IN ('expense', 'purchase')
    `;

    const salesQuery = `
      SELECT COUNT(*) as count
      FROM transactions
      ${dateFilter} AND type = 'sale'
    `;

    const [revenue, expenses, sales] = await Promise.all([
      this.executeQuery(revenueQuery),
      this.executeQuery(expenseQuery),
      this.executeQuery(salesQuery)
    ]);

    return {
      totalRevenue: revenue[0]?.total || 0,
      totalExpenses: expenses[0]?.total || 0,
      totalSales: sales[0]?.count || 0,
      period
    };
  }

  async getFinancialCategories(): Promise<any[]> {
    const query = 'SELECT * FROM financial_categories WHERE is_active = 1 ORDER BY name';
    return await this.executeQuery(query);
  }

  async createFinancialCategory(category: any): Promise<string> {
    const id = `fcat-${Date.now()}`;
    const query = `
      INSERT INTO financial_categories (id, name, name_ar, type, color, icon, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
    `;
    await this.executeUpdate(query, [
      id, category.name, category.nameAr, category.type,
      category.color, category.icon
    ]);
    return id;
  }

  async updateFinancialCategory(id: string, updateData: any): Promise<void> {
    const query = `
      UPDATE financial_categories
      SET name = ?, name_ar = ?, type = ?, color = ?, icon = ?, updated_at = datetime('now')
      WHERE id = ?
    `;
    await this.executeUpdate(query, [
      updateData.name, updateData.nameAr, updateData.type,
      updateData.color, updateData.icon, id
    ]);
  }

  async deleteFinancialCategory(id: string): Promise<void> {
    await this.executeUpdate('UPDATE financial_categories SET is_active = 0 WHERE id = ?', [id]);
  }

  async updateTransaction(id: string, updateData: any): Promise<void> {
    const query = `
      UPDATE transactions
      SET type = ?, amount = ?, description = ?, description_ar = ?, date = ?, updated_at = datetime('now')
      WHERE id = ?
    `;
    await this.executeUpdate(query, [
      updateData.type, updateData.amount, updateData.description,
      updateData.descriptionAr, updateData.date, id
    ]);
  }

  async deleteTransaction(id: string): Promise<void> {
    await this.executeUpdate('DELETE FROM transactions WHERE id = ?', [id]);
  }

  // Dashboard statistics
  async getDashboardStats(): Promise<any> {
    const totalProducts = await this.executeQuery('SELECT COUNT(*) as count FROM products WHERE is_active = 1');
    const totalCustomers = await this.executeQuery('SELECT COUNT(*) as count FROM customers WHERE is_active = 1');
    const totalInvoices = await this.executeQuery('SELECT COUNT(*) as count FROM invoices');
    const totalRevenue = await this.executeQuery('SELECT SUM(total_amount) as total FROM invoices WHERE status = "paid"');
    
    return {
      totalProducts: totalProducts[0]?.count || 0,
      totalCustomers: totalCustomers[0]?.count || 0,
      totalInvoices: totalInvoices[0]?.count || 0,
      totalRevenue: totalRevenue[0]?.total || 0
    };
  }
}

export default new DatabaseService();
