import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Linking,
} from 'react-native';
import {
  Text,
  Card,
  Appbar,
  Button,
  Chip,
  Divider,
  List,
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchCustomerById, deleteCustomer } from '../../store/slices/customerSlice';
import { commonStyles, colors, spacing } from '../../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const CustomerDetailsScreen: React.FC<{ navigation: any; route: any }> = ({ navigation, route }) => {
  const { customerId } = route.params;
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { customers, isLoading } = useSelector((state: RootState) => state.customers);
  const { isRTL } = useSelector((state: RootState) => state.app);

  const [customer, setCustomer] = useState<any>(null);

  useEffect(() => {
    loadCustomer();
  }, [customerId]);

  const loadCustomer = async () => {
    try {
      const customerData = customers.find(c => c.id === customerId);
      if (customerData) {
        setCustomer(customerData);
      } else {
        // Fetch from database if not in store
        await dispatch(fetchCustomerById(customerId));
      }
    } catch (error) {
      console.error('Failed to load customer:', error);
      Alert.alert(t('messages.error'), t('messages.customerNotFound'));
      navigation.goBack();
    }
  };

  const handleCall = () => {
    if (customer?.phone) {
      Linking.openURL(`tel:${customer.phone}`);
    }
  };

  const handleEmail = () => {
    if (customer?.email) {
      Linking.openURL(`mailto:${customer.email}`);
    }
  };

  const handleWhatsApp = () => {
    if (customer?.phone) {
      const phoneNumber = customer.phone.replace(/[^\d]/g, '');
      Linking.openURL(`whatsapp://send?phone=${phoneNumber}`);
    }
  };

  const handleEdit = () => {
    navigation.navigate('EditCustomer', { customerId });
  };

  const handleDelete = () => {
    Alert.alert(
      t('common.confirm'),
      t('messages.deleteCustomerConfirm', { name: customer?.name }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await dispatch(deleteCustomer(customerId));
              Alert.alert(t('messages.success'), t('messages.customerDeleted'));
              navigation.goBack();
            } catch (error) {
              Alert.alert(t('messages.error'), t('messages.operationFailed'));
            }
          },
        },
      ]
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (!customer) {
    return (
      <View style={styles.container}>
        <Appbar.Header>
          <Appbar.BackAction onPress={() => navigation.goBack()} />
          <Appbar.Content title={t('customers.customerDetails')} />
        </Appbar.Header>
        <View style={styles.loadingContainer}>
          <Text>{t('common.loading')}</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={customer.name} />
        <Appbar.Action icon="edit" onPress={handleEdit} />
        <Appbar.Action icon="delete" onPress={handleDelete} />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        {/* Customer Header */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={[styles.customerHeader, isRTL && styles.customerHeaderRTL]}>
              <View style={styles.customerInfo}>
                <Text style={[styles.customerName, isRTL && styles.customerNameRTL]}>
                  {customer.name}
                </Text>
                <Chip
                  mode="outlined"
                  style={[styles.typeChip, { borderColor: customer.type === 'business' ? colors.primary : colors.secondary }]}
                  textStyle={{ color: customer.type === 'business' ? colors.primary : colors.secondary }}
                >
                  {customer.type === 'business' ? t('customers.business') : t('customers.individual')}
                </Chip>
              </View>
              <Chip
                mode="outlined"
                style={[styles.statusChip, { borderColor: customer.isActive ? colors.tertiary : colors.error }]}
                textStyle={{ color: customer.isActive ? colors.tertiary : colors.error }}
              >
                {customer.isActive ? t('common.active') : t('common.inactive')}
              </Chip>
            </View>
          </Card.Content>
        </Card>

        {/* Contact Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('customers.contactInfo')}
            </Text>

            {customer.phone && (
              <List.Item
                title={customer.phone}
                description={t('customers.phone')}
                left={(props) => <List.Icon {...props} icon="phone" />}
                right={(props) => (
                  <View style={[styles.contactActions, isRTL && styles.contactActionsRTL]}>
                    <Button mode="outlined" compact onPress={handleCall}>
                      {t('common.call')}
                    </Button>
                    <Button mode="outlined" compact onPress={handleWhatsApp} style={styles.actionButton}>
                      WhatsApp
                    </Button>
                  </View>
                )}
                style={styles.listItem}
              />
            )}

            {customer.email && (
              <List.Item
                title={customer.email}
                description={t('customers.email')}
                left={(props) => <List.Icon {...props} icon="email" />}
                right={(props) => (
                  <Button mode="outlined" compact onPress={handleEmail}>
                    {t('common.email')}
                  </Button>
                )}
                style={styles.listItem}
              />
            )}

            {customer.address && (
              <List.Item
                title={`${customer.address}${customer.city ? `, ${customer.city}` : ''}${customer.country ? `, ${customer.country}` : ''}`}
                description={t('customers.address')}
                left={(props) => <List.Icon {...props} icon="location-on" />}
                style={styles.listItem}
              />
            )}
          </Card.Content>
        </Card>

        {/* Business Information */}
        {customer.type === 'business' && (
          <Card style={styles.card}>
            <Card.Content>
              <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
                {t('customers.businessInfo')}
              </Text>

              {customer.taxNumber && (
                <List.Item
                  title={customer.taxNumber}
                  description={t('customers.taxNumber')}
                  left={(props) => <List.Icon {...props} icon="receipt" />}
                  style={styles.listItem}
                />
              )}

              <List.Item
                title={formatCurrency(customer.creditLimit || 0)}
                description={t('customers.creditLimit')}
                left={(props) => <List.Icon {...props} icon="credit-card" />}
                style={styles.listItem}
              />

              <List.Item
                title={`${customer.paymentTerms || 30} ${t('customers.days')}`}
                description={t('customers.paymentTerms')}
                left={(props) => <List.Icon {...props} icon="schedule" />}
                style={styles.listItem}
              />
            </Card.Content>
          </Card>
        )}

        {/* Additional Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, isRTL && styles.sectionTitleRTL]}>
              {t('customers.additionalInfo')}
            </Text>

            <List.Item
              title={formatDate(customer.createdAt)}
              description={t('customers.customerSince')}
              left={(props) => <List.Icon {...props} icon="calendar-today" />}
              style={styles.listItem}
            />

            <List.Item
              title="0" // This would come from actual invoice data
              description={t('customers.totalOrders')}
              left={(props) => <List.Icon {...props} icon="shopping-cart" />}
              style={styles.listItem}
            />

            <List.Item
              title={formatCurrency(0)} // This would come from actual invoice data
              description={t('customers.totalSpent')}
              left={(props) => <List.Icon {...props} icon="attach-money" />}
              style={styles.listItem}
            />

            {customer.notes && (
              <View style={styles.notesSection}>
                <Text style={[styles.notesTitle, isRTL && styles.notesTitleRTL]}>
                  {t('customers.notes')}
                </Text>
                <Text style={[styles.notesText, isRTL && styles.notesTextRTL]}>
                  {customer.notes}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.actionButtons}>
              <Button
                mode="contained"
                onPress={() => navigation.navigate('CreateInvoice', { customerId })}
                style={styles.actionButton}
                icon="receipt"
              >
                {t('invoices.createInvoice')}
              </Button>
              <Button
                mode="outlined"
                onPress={() => navigation.navigate('CustomerInvoices', { customerId })}
                style={styles.actionButton}
                icon="history"
              >
                {t('customers.viewHistory')}
              </Button>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    marginBottom: spacing.md,
    borderRadius: 12,
    elevation: 2,
  },
  customerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  customerHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  customerInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  customerName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginRight: spacing.md,
  },
  customerNameRTL: {
    marginRight: 0,
    marginLeft: spacing.md,
    textAlign: 'right',
  },
  typeChip: {
    marginLeft: spacing.sm,
  },
  statusChip: {
    marginLeft: spacing.sm,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
  },
  sectionTitleRTL: {
    textAlign: 'right',
  },
  listItem: {
    paddingHorizontal: 0,
  },
  contactActions: {
    flexDirection: 'row',
  },
  contactActionsRTL: {
    flexDirection: 'row-reverse',
  },
  actionButton: {
    marginLeft: spacing.sm,
    marginBottom: spacing.sm,
  },
  notesSection: {
    marginTop: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.surfaceVariant,
    borderRadius: 8,
  },
  notesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurfaceVariant,
    marginBottom: spacing.sm,
  },
  notesTitleRTL: {
    textAlign: 'right',
  },
  notesText: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
    lineHeight: 20,
  },
  notesTextRTL: {
    textAlign: 'right',
  },
  actionButtons: {
    flexDirection: 'column',
  },
});

export default CustomerDetailsScreen;
