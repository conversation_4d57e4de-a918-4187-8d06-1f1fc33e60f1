import * as SQLite from 'expo-sqlite';

export const DATABASE_NAME = 'inventory_management.db';
export const DATABASE_VERSION = 1;

export const createTables = async (db: SQLite.SQLiteDatabase) => {
  // Users table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'employee')),
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `);

  // Warehouses table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS warehouses (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      location TEXT NOT NULL,
      description TEXT,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `);

  // Categories table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS categories (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      name_ar TEXT NOT NULL,
      description TEXT,
      description_ar TEXT,
      parent_id TEXT,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      FOREIGN KEY (parent_id) REFERENCES categories (id)
    );
  `);

  // Products table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS products (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      name_ar TEXT NOT NULL,
      description TEXT,
      description_ar TEXT,
      sku TEXT UNIQUE NOT NULL,
      barcode TEXT,
      category_id TEXT NOT NULL,
      unit_price REAL NOT NULL,
      cost_price REAL NOT NULL,
      min_stock_level INTEGER NOT NULL DEFAULT 0,
      max_stock_level INTEGER NOT NULL DEFAULT 1000,
      unit TEXT NOT NULL,
      is_active INTEGER NOT NULL DEFAULT 1,
      image_url TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      FOREIGN KEY (category_id) REFERENCES categories (id)
    );
  `);

  // Stock table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS stock (
      id TEXT PRIMARY KEY,
      product_id TEXT NOT NULL,
      warehouse_id TEXT NOT NULL,
      quantity INTEGER NOT NULL DEFAULT 0,
      carton_quantity INTEGER NOT NULL DEFAULT 0,
      pieces_per_carton INTEGER NOT NULL DEFAULT 1,
      reserved_quantity INTEGER NOT NULL DEFAULT 0,
      last_updated TEXT NOT NULL,
      FOREIGN KEY (product_id) REFERENCES products (id),
      FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
      UNIQUE (product_id, warehouse_id)
    );
  `);

  // Customers table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS customers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      name_ar TEXT NOT NULL,
      email TEXT,
      phone TEXT NOT NULL,
      whatsapp TEXT,
      address TEXT NOT NULL,
      address_ar TEXT NOT NULL,
      city TEXT NOT NULL,
      city_ar TEXT NOT NULL,
      country TEXT NOT NULL,
      country_ar TEXT NOT NULL,
      customer_type TEXT NOT NULL CHECK (customer_type IN ('individual', 'business')),
      tax_number TEXT,
      credit_limit REAL NOT NULL DEFAULT 0,
      payment_terms INTEGER NOT NULL DEFAULT 30,
      is_active INTEGER NOT NULL DEFAULT 1,
      notes TEXT,
      notes_ar TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `);

  // Suppliers table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS suppliers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      name_ar TEXT NOT NULL,
      email TEXT,
      phone TEXT NOT NULL,
      address TEXT NOT NULL,
      address_ar TEXT NOT NULL,
      city TEXT NOT NULL,
      city_ar TEXT NOT NULL,
      country TEXT NOT NULL,
      country_ar TEXT NOT NULL,
      tax_number TEXT,
      payment_terms INTEGER NOT NULL DEFAULT 30,
      is_active INTEGER NOT NULL DEFAULT 1,
      notes TEXT,
      notes_ar TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `);

  // Invoices table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS invoices (
      id TEXT PRIMARY KEY,
      invoice_number TEXT UNIQUE NOT NULL,
      customer_id TEXT NOT NULL,
      warehouse_id TEXT NOT NULL,
      invoice_date TEXT NOT NULL,
      due_date TEXT NOT NULL,
      subtotal REAL NOT NULL,
      tax_amount REAL NOT NULL DEFAULT 0,
      discount_amount REAL NOT NULL DEFAULT 0,
      total_amount REAL NOT NULL,
      paid_amount REAL NOT NULL DEFAULT 0,
      status TEXT NOT NULL CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
      payment_status TEXT NOT NULL CHECK (payment_status IN ('pending', 'partial', 'paid')),
      notes TEXT,
      notes_ar TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      FOREIGN KEY (customer_id) REFERENCES customers (id),
      FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
    );
  `);

  // Invoice items table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS invoice_items (
      id TEXT PRIMARY KEY,
      invoice_id TEXT NOT NULL,
      product_id TEXT NOT NULL,
      quantity INTEGER NOT NULL,
      unit_price REAL NOT NULL,
      discount_percent REAL NOT NULL DEFAULT 0,
      tax_percent REAL NOT NULL DEFAULT 0,
      total_amount REAL NOT NULL,
      FOREIGN KEY (invoice_id) REFERENCES invoices (id),
      FOREIGN KEY (product_id) REFERENCES products (id)
    );
  `);

  // Purchase orders table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS purchase_orders (
      id TEXT PRIMARY KEY,
      po_number TEXT UNIQUE NOT NULL,
      supplier_id TEXT NOT NULL,
      warehouse_id TEXT NOT NULL,
      order_date TEXT NOT NULL,
      expected_date TEXT NOT NULL,
      received_date TEXT,
      subtotal REAL NOT NULL,
      tax_amount REAL NOT NULL DEFAULT 0,
      total_amount REAL NOT NULL,
      status TEXT NOT NULL CHECK (status IN ('draft', 'sent', 'confirmed', 'received', 'cancelled')),
      notes TEXT,
      notes_ar TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
      FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
    );
  `);

  // Purchase order items table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS purchase_order_items (
      id TEXT PRIMARY KEY,
      purchase_order_id TEXT NOT NULL,
      product_id TEXT NOT NULL,
      quantity INTEGER NOT NULL,
      unit_price REAL NOT NULL,
      received_quantity INTEGER NOT NULL DEFAULT 0,
      total_amount REAL NOT NULL,
      FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders (id),
      FOREIGN KEY (product_id) REFERENCES products (id)
    );
  `);

  // Transactions table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS transactions (
      id TEXT PRIMARY KEY,
      type TEXT NOT NULL CHECK (type IN ('sale', 'purchase', 'adjustment', 'transfer')),
      reference_id TEXT NOT NULL,
      amount REAL NOT NULL,
      description TEXT NOT NULL,
      description_ar TEXT NOT NULL,
      date TEXT NOT NULL,
      created_at TEXT NOT NULL
    );
  `);

  // Financial categories table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS financial_categories (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      name_ar TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      color TEXT NOT NULL DEFAULT '#2196F3',
      icon TEXT NOT NULL DEFAULT 'category',
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `);

  // Stock movements table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS stock_movements (
      id TEXT PRIMARY KEY,
      product_id TEXT NOT NULL,
      warehouse_id TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('in', 'out', 'adjustment', 'transfer')),
      quantity INTEGER NOT NULL,
      reference_type TEXT NOT NULL CHECK (reference_type IN ('invoice', 'purchase_order', 'adjustment', 'transfer')),
      reference_id TEXT NOT NULL,
      notes TEXT,
      notes_ar TEXT,
      created_at TEXT NOT NULL,
      FOREIGN KEY (product_id) REFERENCES products (id),
      FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
    );
  `);

  // Create indexes for better performance
  await db.execAsync(`
    CREATE INDEX IF NOT EXISTS idx_products_sku ON products (sku);
    CREATE INDEX IF NOT EXISTS idx_products_category ON products (category_id);
    CREATE INDEX IF NOT EXISTS idx_stock_product_warehouse ON stock (product_id, warehouse_id);
    CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices (customer_id);
    CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices (invoice_date);
    CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice ON invoice_items (invoice_id);
    CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders (supplier_id);
    CREATE INDEX IF NOT EXISTS idx_purchase_order_items_po ON purchase_order_items (purchase_order_id);
    CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions (date);
    CREATE INDEX IF NOT EXISTS idx_stock_movements_product ON stock_movements (product_id);
    CREATE INDEX IF NOT EXISTS idx_stock_movements_date ON stock_movements (created_at);
  `);
};

export const insertSampleData = async (db: SQLite.SQLiteDatabase) => {
  // Insert default admin user
  await db.execAsync(`
    INSERT OR IGNORE INTO users (id, username, email, password_hash, role, created_at, updated_at)
    VALUES ('admin-001', 'admin', '<EMAIL>', 'admin123', 'admin', datetime('now'), datetime('now'));
  `);

  // Insert default warehouse
  await db.execAsync(`
    INSERT OR IGNORE INTO warehouses (id, name, location, description, created_at, updated_at)
    VALUES ('warehouse-001', 'Main Warehouse', 'Main Location', 'Primary warehouse for inventory', datetime('now'), datetime('now'));
  `);

  // Insert sample categories
  await db.execAsync(`
    INSERT OR IGNORE INTO categories (id, name, name_ar, description, description_ar, created_at, updated_at)
    VALUES 
    ('cat-001', 'Electronics', 'إلكترونيات', 'Electronic devices and accessories', 'الأجهزة الإلكترونية والإكسسوارات', datetime('now'), datetime('now')),
    ('cat-002', 'Clothing', 'ملابس', 'Clothing and fashion items', 'الملابس وعناصر الموضة', datetime('now'), datetime('now')),
    ('cat-003', 'Food & Beverages', 'طعام ومشروبات', 'Food and beverage products', 'منتجات الطعام والمشروبات', datetime('now'), datetime('now'));
  `);
};
